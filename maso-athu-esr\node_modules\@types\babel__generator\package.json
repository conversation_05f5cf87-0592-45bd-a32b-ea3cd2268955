{"name": "@types/babel__generator", "version": "7.27.0", "description": "TypeScript definitions for @babel/generator", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__generator", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "yortus", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "githubUsername": "khell", "url": "https://github.com/khell"}, {"name": "Lyanbin", "githubUsername": "Lyanbin", "url": "https://github.com/Lyanbin"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__generator"}, "scripts": {}, "dependencies": {"@babel/types": "^7.0.0"}, "peerDependencies": {}, "typesPublisherContentHash": "b5c7deac65dbd6ab9b313d1d71c86afe4383b881dcb4e3b3ac51dab07b8f95fb", "typeScriptVersion": "5.1"}