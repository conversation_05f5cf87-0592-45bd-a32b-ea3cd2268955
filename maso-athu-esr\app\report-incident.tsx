import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Modal,
    Platform,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// Updated incident types and subcategories based on EWER format
const INCIDENT_TYPES = {
  'Security (61)': [
    'Physical violence (1)',
    'Homicide (2)',
    'Robbery (3)',
    'Destruction of property (4)',
    'Arson (5)',
    'Demonstrations (peaceful or violent) (6)',
    'Drugs and trafficking (7)',
    'Ritualistic killings (8)'
  ],
  'Health and Environment (62)': [
    'Disease outbreaks (Covid, Ebola, etc.) (1)',
    'Natural disasters (2)',
    'Environmental pollution or illegal dumping (3)'
  ],
  'Gender (63)': [
    'Physical violence (1)',
    'Psychological violence (2)',
    'Sexual violence (3)',
    'Restrictions and lack of equal opportunities (4)'
  ],
  'Governance and Human Rights Violations (64)': [
    'Corruption and bribery (1)',
    'Land/boundary disputes (2)',
    'Torture, ill-treatment (3)',
    'Non-discrimination and equality (4)',
    'Presumption of innocence (5)',
    'Accountability (6)',
    'Equal participation (7)',
    'Effective remedy (8)',
    'Access to Education (9)',
    'Health and safety (10)',
    'Right to Peaceful assembly (11)',
    'Illegal Arrests (12)',
    'Use of force (13)',
    'Expression of views (14)',
    'Access to information (15)',
    'Access to basic social services (roads, food, electricity, safe drinking water, etc.) (16)'
  ],
  'Elections (65)': [
    'Fraud and irregularities (1)',
    'Vote buying (2)',
    'Voter intimidation and coercion (3)',
    'Intimidation or attacks on polling workers (4)',
    'Violence against women in elections (5)',
    'Hate speech (6)',
    'Destruction of polling material or installations (7)',
    'Voter trucking (8)'
  ]
};

const DISTRICTS = [
  'Blantyre', 'Lilongwe', 'Mzuzu', 'Zomba', 'Kasungu', 'Mangochi', 'Salima',
  'Nkhotakota', 'Ntchisi', 'Dowa', 'Mchinji', 'Dedza', 'Ntcheu', 'Balaka',
  'Machinga', 'Chiradzulu', 'Nsanje', 'Chikwawa', 'Thyolo', 'Mulanje',
  'Phalombe', 'Neno', 'Mwanza', 'Karonga', 'Chitipa', 'Rumphi', 'Nkhata Bay',
  'Likoma'
];

export default function ReportIncidentScreen() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    incidentType: '',
    subCategory: '',
    date: '',
    district: '',
    location: '',
  });

  const [attachments, setAttachments] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showIncidentTypeModal, setShowIncidentTypeModal] = useState(false);
  const [showSubCategoryModal, setShowSubCategoryModal] = useState(false);
  const [showDistrictModal, setShowDistrictModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      // Reset subcategory when incident type changes
      ...(field === 'incidentType' ? { subCategory: '' } : {})
    }));
  };

  const handleDateChange = (event: any, date?: Date) => {
    setShowDatePicker(false);
    if (date) {
      setSelectedDate(date);
      const formattedDate = date.toLocaleDateString('en-GB'); // DD/MM/YYYY format
      handleInputChange('date', formattedDate);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Select date...';
    return dateString;
  };

  const addAttachment = () => {
    Alert.alert(
      'Add Attachment',
      'Choose attachment type',
      [
        { text: 'Photo from Gallery', onPress: () => pickImageFromGallery() },
        { text: 'Take Photo', onPress: () => takePhoto() },
        { text: 'Video from Gallery', onPress: () => pickVideoFromGallery() },
        { text: 'Record Video', onPress: () => recordVideo() },
        { text: 'Document', onPress: () => pickDocument() },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const pickImageFromGallery = async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access gallery is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newAttachment = {
          id: Date.now().toString(),
          type: 'image',
          name: asset.fileName || `image_${Date.now()}.jpg`,
          uri: asset.uri,
          size: asset.fileSize ? `${(asset.fileSize / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'
        };
        setAttachments(prev => [...prev, newAttachment]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image from gallery');
    }
  };

  const takePhoto = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newAttachment = {
          id: Date.now().toString(),
          type: 'image',
          name: `photo_${Date.now()}.jpg`,
          uri: asset.uri,
          size: asset.fileSize ? `${(asset.fileSize / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'
        };
        setAttachments(prev => [...prev, newAttachment]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const pickVideoFromGallery = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access gallery is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newAttachment = {
          id: Date.now().toString(),
          type: 'video',
          name: asset.fileName || `video_${Date.now()}.mp4`,
          uri: asset.uri,
          size: asset.fileSize ? `${(asset.fileSize / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'
        };
        setAttachments(prev => [...prev, newAttachment]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick video from gallery');
    }
  };

  const recordVideo = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newAttachment = {
          id: Date.now().toString(),
          type: 'video',
          name: `video_${Date.now()}.mp4`,
          uri: asset.uri,
          size: asset.fileSize ? `${(asset.fileSize / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'
        };
        setAttachments(prev => [...prev, newAttachment]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to record video');
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newAttachment = {
          id: Date.now().toString(),
          type: 'document',
          name: asset.name,
          uri: asset.uri,
          size: asset.size ? `${(asset.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'
        };
        setAttachments(prev => [...prev, newAttachment]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document');
    }
  };

  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(att => att.id !== id));
  };

  const handleSubmit = async () => {
    // Validation
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter incident title');
      return;
    }
    if (!formData.description.trim()) {
      Alert.alert('Error', 'Please enter incident description');
      return;
    }
    if (!formData.incidentType) {
      Alert.alert('Error', 'Please select incident type');
      return;
    }
    if (!formData.subCategory) {
      Alert.alert('Error', 'Please select incident subcategory');
      return;
    }
    if (!formData.date) {
      Alert.alert('Error', 'Please enter incident date');
      return;
    }
    if (!formData.district) {
      Alert.alert('Error', 'Please select district');
      return;
    }
    if (!formData.location.trim()) {
      Alert.alert('Error', 'Please enter location');
      return;
    }

    setIsSubmitting(true);
    
    // Simulate submission
    setTimeout(() => {
      setIsSubmitting(false);
      Alert.alert(
        'Success',
        'Incident report submitted successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    }, 2000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerIconContainer}>
            <Ionicons name="warning" size={28} color="#EF4444" />
          </View>
          <Text style={styles.headerTitle}>Report Incident</Text>
          <Text style={styles.headerSubtitle}>
            Document electoral incidents for monitoring and response
          </Text>
        </View>

        {/* Form */}
        <View style={styles.formContainer}>
          {/* Incident Title */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              Incident Title <Text style={styles.chichewa}>(Lemba chachitika mwachidule)</Text>
            </Text>
            <TextInput
              style={styles.textInput}
              value={formData.title}
              onChangeText={(text) => handleInputChange('title', text)}
              placeholder="Enter incident title"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          {/* Description */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              Description <Text style={styles.chichewa}>(Longosolani chachitikachi mwatsatane-tsatane)</Text>
            </Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => handleInputChange('description', text)}
              placeholder="Provide detailed description of the incident..."
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Incident Type & Sub Category Row */}
          <View style={styles.rowContainer}>
            <View style={styles.halfFieldContainer}>
              <Text style={styles.fieldLabel}>Incident Type</Text>
              <TouchableOpacity
                style={styles.dropdownButton}
                onPress={() => setShowIncidentTypeModal(true)}
              >
                <Text style={[styles.dropdownText, !formData.incidentType && styles.placeholderText]}>
                  {formData.incidentType || 'Select type...'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {formData.incidentType && (
              <View style={styles.halfFieldContainer}>
                <Text style={styles.fieldLabel}>Sub Category</Text>
                <TouchableOpacity
                  style={styles.dropdownButton}
                  onPress={() => setShowSubCategoryModal(true)}
                >
                  <Text style={[styles.dropdownText, !formData.subCategory && styles.placeholderText]}>
                    {formData.subCategory || 'Select sub category...'}
                  </Text>
                  <Ionicons name="chevron-down" size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
            )}
          </View>

          {/* Date */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Date of Incident</Text>
            <TouchableOpacity
              style={styles.dropdownButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={[styles.dropdownText, !formData.date && styles.placeholderText]}>
                {formatDate(formData.date)}
              </Text>
              <Ionicons name="calendar-outline" size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* District & Location Row */}
          <View style={styles.rowContainer}>
            <View style={styles.halfFieldContainer}>
              <Text style={styles.fieldLabel}>District</Text>
              <TouchableOpacity
                style={styles.dropdownButton}
                onPress={() => setShowDistrictModal(true)}
              >
                <Text style={[styles.dropdownText, !formData.district && styles.placeholderText]}>
                  {formData.district || 'Select district...'}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <View style={styles.halfFieldContainer}>
              <Text style={styles.fieldLabel}>Location</Text>
              <TextInput
                style={styles.textInput}
                value={formData.location}
                onChangeText={(text) => handleInputChange('location', text)}
                placeholder="Enter specific location"
                placeholderTextColor="#9CA3AF"
              />
            </View>
          </View>

          {/* Attachments */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Attachments</Text>
            
            {attachments.map((attachment) => (
              <View key={attachment.id} style={styles.attachmentItem}>
                <View style={styles.attachmentInfo}>
                  <Ionicons 
                    name={
                      attachment.type === 'photo' ? 'image' :
                      attachment.type === 'video' ? 'videocam' : 'document'
                    } 
                    size={20} 
                    color="#6B7280" 
                  />
                  <View style={styles.attachmentDetails}>
                    <Text style={styles.attachmentName}>{attachment.name}</Text>
                    <Text style={styles.attachmentSize}>{attachment.size}</Text>
                  </View>
                </View>
                <TouchableOpacity 
                  onPress={() => removeAttachment(attachment.id)}
                  style={styles.removeButton}
                >
                  <Ionicons name="close-circle" size={20} color="#EF4444" />
                </TouchableOpacity>
              </View>
            ))}

            <TouchableOpacity style={styles.addAttachmentButton} onPress={addAttachment}>
              <Ionicons name="add-circle-outline" size={24} color="#059669" />
              <Text style={styles.addAttachmentText}>Add Attachment</Text>
            </TouchableOpacity>
          </View>

          {/* Submit Button */}
          <TouchableOpacity 
            style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <Text style={styles.submitButtonText}>
              {isSubmitting ? 'Submitting Report...' : 'Submit Incident Report'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Incident Type Modal */}
      <Modal
        visible={showIncidentTypeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowIncidentTypeModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowIncidentTypeModal(false)}
        >
          <TouchableOpacity
            style={styles.modalContent}
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Incident Type</Text>
              <TouchableOpacity onPress={() => setShowIncidentTypeModal(false)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {Object.keys(INCIDENT_TYPES).map((type) => (
                <TouchableOpacity
                  key={type}
                  style={styles.modalItem}
                  onPress={() => {
                    handleInputChange('incidentType', type);
                    setShowIncidentTypeModal(false);
                  }}
                >
                  <Text style={styles.modalItemText}>{type}</Text>
                  {formData.incidentType === type && (
                    <Ionicons name="checkmark" size={20} color="#059669" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>

      {/* Sub Category Modal */}
      <Modal
        visible={showSubCategoryModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSubCategoryModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSubCategoryModal(false)}
        >
          <TouchableOpacity
            style={styles.modalContent}
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Sub Category</Text>
              <TouchableOpacity onPress={() => setShowSubCategoryModal(false)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {formData.incidentType && INCIDENT_TYPES[formData.incidentType].map((subCat) => (
                <TouchableOpacity
                  key={subCat}
                  style={styles.modalItem}
                  onPress={() => {
                    handleInputChange('subCategory', subCat);
                    setShowSubCategoryModal(false);
                  }}
                >
                  <Text style={styles.modalItemText}>{subCat}</Text>
                  {formData.subCategory === subCat && (
                    <Ionicons name="checkmark" size={20} color="#059669" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>

      {/* District Modal */}
      <Modal
        visible={showDistrictModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDistrictModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowDistrictModal(false)}
        >
          <TouchableOpacity
            style={styles.modalContent}
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select District</Text>
              <TouchableOpacity onPress={() => setShowDistrictModal(false)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalList}>
              {DISTRICTS.map((district) => (
                <TouchableOpacity
                  key={district}
                  style={styles.modalItem}
                  onPress={() => {
                    handleInputChange('district', district);
                    setShowDistrictModal(false);
                  }}
                >
                  <Text style={styles.modalItemText}>{district}</Text>
                  {formData.district === district && (
                    <Ionicons name="checkmark" size={20} color="#059669" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>

      {/* Date Picker Modal */}
      <Modal
        visible={showDatePicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDatePicker(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowDatePicker(false)}
        >
          <TouchableOpacity
            style={styles.modalContent}
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Date</Text>
              <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>Select incident date:</Text>
              <View style={styles.dateInputContainer}>
                <TextInput
                  style={styles.dateInput}
                  value={formData.date}
                  onChangeText={(text) => handleInputChange('date', text)}
                  placeholder="DD/MM/YYYY"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.datePickerButtons}>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => {
                    const today = new Date().toLocaleDateString('en-GB');
                    handleInputChange('date', today);
                  }}
                >
                  <Text style={styles.datePickerButtonText}>Today</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => {
                    const yesterday = new Date();
                    yesterday.setDate(yesterday.getDate() - 1);
                    const yesterdayFormatted = yesterday.toLocaleDateString('en-GB');
                    handleInputChange('date', yesterdayFormatted);
                  }}
                >
                  <Text style={styles.datePickerButtonText}>Yesterday</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.datePickerButton, styles.customDateButton]}
                  onPress={() => {
                    // Focus on the text input for custom date entry
                    Alert.alert('Custom Date', 'Enter your custom date in the field above in DD/MM/YYYY format');
                  }}
                >
                  <Text style={[styles.datePickerButtonText, styles.customDateButtonText]}>Custom</Text>
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                style={styles.confirmDateButton}
                onPress={() => setShowDatePicker(false)}
              >
                <Text style={styles.confirmDateButtonText}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 32,
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: 20,
  },
  headerIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#FEF2F2',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  formContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  fieldContainer: {
    marginBottom: 24,
  },
  rowContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  halfFieldContainer: {
    flex: 1,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  chichewa: {
    fontSize: 14,
    fontWeight: '400',
    color: '#6B7280',
    fontStyle: 'italic',
  },
  textInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'ios' ? 16 : 14,
    fontSize: 16,
    color: '#111827',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  dropdownButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'ios' ? 16 : 14,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#111827',
    flex: 1,
  },
  placeholderText: {
    color: '#9CA3AF',
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  attachmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  attachmentDetails: {
    marginLeft: 12,
    flex: 1,
  },
  attachmentName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  attachmentSize: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  removeButton: {
    padding: 4,
  },
  addAttachmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0FDF4',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderColor: '#059669',
    borderStyle: 'dashed',
  },
  addAttachmentText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#059669',
    marginLeft: 8,
  },
  submitButton: {
    backgroundColor: '#059669',
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#059669',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0,
    elevation: 0,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '700',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: '80%',
    minHeight: '50%',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  modalList: {
    flex: 1,
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  modalItemText: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  datePickerContainer: {
    padding: 20,
  },
  datePickerLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  dateInputContainer: {
    marginBottom: 20,
  },
  dateInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: Platform.OS === 'ios' ? 16 : 14,
    fontSize: 16,
    color: '#111827',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlign: 'center',
  },
  datePickerButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  datePickerButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  datePickerButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  customDateButton: {
    backgroundColor: '#EBF4FF',
    borderColor: '#3B82F6',
  },
  customDateButtonText: {
    color: '#3B82F6',
  },
  confirmDateButton: {
    backgroundColor: '#059669',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  confirmDateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
