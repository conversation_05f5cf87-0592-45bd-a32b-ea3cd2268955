import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Image,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

export default function HomeScreen() {
  const [showSettingsDropdown, setShowSettingsDropdown] = useState(false);

  const handleCardPress = (cardType: string) => {
    if (cardType === 'Report Incident') {
      router.push('/report-incident');
    } else if (cardType === 'My Incidents') {
      router.push('/my-incidents');
    } else if (cardType === 'Observer Day') {
      router.push('/observer-checklist');
    } else if (cardType === 'My Observer Day Reports') {
      router.push('/my-observer-reports');
    } else {
      Alert.alert('Feature', `${cardType} feature will be implemented soon!`);
    }
  };

  const handleSettingsPress = () => {
    setShowSettingsDropdown(!showSettingsDropdown);
  };

  const handleProfilePress = () => {
    setShowSettingsDropdown(false);
    router.push('/profile');
  };

  const handleChangePasswordPress = () => {
    setShowSettingsDropdown(false);
    router.push('/change-password');
  };

  const handleLogout = () => {
    setShowSettingsDropdown(false);
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => router.replace('/login')
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image
            source={require('../../assets/images/logo.png')}
            style={styles.headerLogo}
            resizeMode="contain"
          />
          <View style={styles.headerTextContainer}>
            <Text style={styles.welcomeText}>Welcome back!</Text>
            <Text style={styles.subtitleText}>Election Monitoring Dashboard</Text>
          </View>
        </View>
        <View style={styles.settingsContainer}>
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={handleSettingsPress}
          >
            <Ionicons name="settings-outline" size={24} color="#374151" />
          </TouchableOpacity>

          {showSettingsDropdown && (
            <View style={styles.dropdown}>
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={handleProfilePress}
              >
                <Ionicons name="person-outline" size={20} color="#374151" />
                <Text style={styles.dropdownText}>My Profile</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={handleChangePasswordPress}
              >
                <Ionicons name="lock-closed-outline" size={20} color="#374151" />
                <Text style={styles.dropdownText}>Change Password</Text>
              </TouchableOpacity>
              <View style={styles.dropdownDivider} />
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={handleLogout}
              >
                <Ionicons name="log-out-outline" size={20} color="#EF4444" />
                <Text style={[styles.dropdownText, { color: '#EF4444' }]}>Logout</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Cards Grid */}
        <View style={styles.cardsContainer}>
          <TouchableOpacity
            style={[styles.card, styles.reportCard]}
            onPress={() => handleCardPress('Report Incident')}
          >
            <View style={styles.cardIconContainer}>
              <Ionicons name="warning" size={32} color="#FFFFFF" />
            </View>
            <Text style={styles.cardTitle}>Report Incident</Text>
            <Text style={styles.cardDescription}>
              Report election irregularities and incidents
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.card, styles.observerCard]}
            onPress={() => handleCardPress('Observer Day')}
          >
            <View style={styles.cardIconContainer}>
              <Ionicons name="eye" size={32} color="#FFFFFF" />
            </View>
            <Text style={styles.cardTitle}>Observer Day</Text>
            <Text style={styles.cardDescription}>
              Complete daily observer checklist
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.card, styles.incidentsCard]}
            onPress={() => handleCardPress('My Incidents')}
          >
            <View style={styles.cardIconContainer}>
              <Ionicons name="document-text" size={32} color="#FFFFFF" />
            </View>
            <Text style={styles.cardTitle}>My Incidents</Text>
            <Text style={styles.cardDescription}>
              View your reported incidents
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.card, styles.reportsCard]}
            onPress={() => handleCardPress('My Observer Day Reports')}
          >
            <View style={styles.cardIconContainer}>
              <Ionicons name="analytics" size={32} color="#FFFFFF" />
            </View>
            <Text style={styles.cardTitle}>My Observer Day Reports</Text>
            <Text style={styles.cardDescription}>
              View your observer day reports
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerLogo: {
    width: 40,
    height: 40,
    marginRight: 12,
  },
  headerTextContainer: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  subtitleText: {
    fontSize: 13,
    color: '#6B7280',
    marginTop: 2,
  },
  settingsContainer: {
    position: 'relative',
  },
  settingsButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  dropdown: {
    position: 'absolute',
    top: 45,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    minWidth: 180,
    zIndex: 1000,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  dropdownText: {
    fontSize: 16,
    color: '#374151',
  },
  dropdownDivider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 16,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  cardsContainer: {
    paddingVertical: 20,
    gap: 16,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: 8,
  },
  reportCard: {
    backgroundColor: '#EF4444',
  },
  observerCard: {
    backgroundColor: '#3B82F6',
  },
  incidentsCard: {
    backgroundColor: '#10B981',
  },
  reportsCard: {
    backgroundColor: '#8B5CF6',
  },
  cardIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
});
