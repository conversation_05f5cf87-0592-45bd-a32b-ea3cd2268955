import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>dal,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

// Import the voter centers data
import voterCentersData from '../malawi_voter_centers.json';

// Political parties for dropdown
const POLITICAL_PARTIES = [
  'Malawi Congress Party (MCP)',
  'Democratic Progressive Party (DPP)',
  'United Transformation Movement (UTM)',
  'Alliance for Democracy (AFORD)',
  'People\'s Party (PP)',
  'United Democratic Front (UDF)',
  'Malawi Forum for Unity and Development (MAFUNDE)',
  'Republican Party (RP)',
  'New Labour Party (NLP)',
  'Petra Party',
  'Other'
];

// Timeline periods
const TIMELINE_PERIODS = [
  { id: 'morning', label: 'Morning (6am - 10am)', timeRange: '6:00 AM - 10:00 AM' },
  { id: 'afternoon', label: 'Afternoon (10am - 2pm)', timeRange: '10:00 AM - 2:00 PM' },
  { id: 'evening', label: 'Evening (2pm - 4pm)', timeRange: '2:00 PM - 4:00 PM' },
  { id: 'votecount', label: 'Vote Count (4pm - 12am)', timeRange: '4:00 PM - 12:00 AM' }
];

interface PoliticalPartyData {
  party: string;
  count: number;
}

interface FormData {
  // Location data
  district: string;
  constituency: string;
  ward: string;
  center: string;
  
  // Timeline responses
  morning: { [key: string]: any };
  afternoon: { [key: string]: any };
  evening: { [key: string]: any };
  votecount: { [key: string]: any };
}

export default function ObserverChecklistScreen() {
  const [currentStep, setCurrentStep] = useState(0); // 0: Location, 1: Timeline selection, 2+: Questions
  const [selectedTimeline, setSelectedTimeline] = useState('');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    district: '',
    constituency: '',
    ward: '',
    center: '',
    morning: {},
    afternoon: {},
    evening: {},
    votecount: {}
  });

  // Location selection states
  const [showDistrictModal, setShowDistrictModal] = useState(false);
  const [showConstituencyModal, setShowConstituencyModal] = useState(false);
  const [showWardModal, setShowWardModal] = useState(false);
  const [showCenterModal, setShowCenterModal] = useState(false);

  // Derived data from selections
  const [availableConstituencies, setAvailableConstituencies] = useState([]);
  const [availableWards, setAvailableWards] = useState([]);
  const [availableCenters, setAvailableCenters] = useState([]);

  // Get districts from the JSON data
  const districts = voterCentersData.malawi_voter_registration_centers.districts;

  // Update available options based on selections
  useEffect(() => {
    if (formData.district) {
      const selectedDistrict = districts.find(d => d.district_name === formData.district);
      setAvailableConstituencies(selectedDistrict?.constituencies || []);
      setFormData(prev => ({ ...prev, constituency: '', ward: '', center: '' }));
    }
  }, [formData.district]);

  useEffect(() => {
    if (formData.constituency) {
      const selectedDistrict = districts.find(d => d.district_name === formData.district);
      const selectedConstituency = selectedDistrict?.constituencies.find(c => c.constituency_name === formData.constituency);
      setAvailableWards(selectedConstituency?.wards || []);
      setFormData(prev => ({ ...prev, ward: '', center: '' }));
    }
  }, [formData.constituency]);

  useEffect(() => {
    if (formData.ward) {
      const selectedDistrict = districts.find(d => d.district_name === formData.district);
      const selectedConstituency = selectedDistrict?.constituencies.find(c => c.constituency_name === formData.constituency);
      const selectedWard = selectedConstituency?.wards.find(w => w.ward_name === formData.ward);
      setAvailableCenters(selectedWard?.polling_centers || []);
      setFormData(prev => ({ ...prev, center: '' }));
    }
  }, [formData.ward]);

  const handleLocationSelection = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Get current questions based on selected timeline
  const getCurrentQuestions = () => {
    if (!selectedTimeline) return [];
    return OBSERVER_QUESTIONS[selectedTimeline as keyof typeof OBSERVER_QUESTIONS] || [];
  };

  const currentQuestions = getCurrentQuestions();
  const totalQuestions = currentQuestions.length;

  const handleQuestionResponse = (questionId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [selectedTimeline]: {
        ...prev[selectedTimeline as keyof FormData],
        [questionId]: value
      }
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      // All questions completed, submit or go to summary
      handleSubmitChecklist();
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    } else {
      // Go back to timeline selection
      setCurrentStep(1);
      setCurrentQuestionIndex(0);
    }
  };

  const handleSubmitChecklist = () => {
    // Here you would typically submit the data to your backend
    Alert.alert(
      'Checklist Completed',
      'Your observer checklist has been submitted successfully!',
      [
        {
          text: 'OK',
          onPress: () => router.back()
        }
      ]
    );
  };

  const handleNextStep = () => {
    if (currentStep === 0) {
      // Validate location selection
      if (!formData.district || !formData.constituency || !formData.ward || !formData.center) {
        Alert.alert('Incomplete Selection', 'Please select district, constituency, ward, and center before proceeding.');
        return;
      }
    } else if (currentStep === 1) {
      // Validate timeline selection
      if (!selectedTimeline) {
        Alert.alert('Select Timeline', 'Please select a timeline period to continue.');
        return;
      }
      // Reset question index when starting questions
      setCurrentQuestionIndex(0);
    }
    setCurrentStep(prev => prev + 1);
  };

  const handlePreviousStep = () => {
    setCurrentStep(prev => Math.max(0, prev - 1));
  };

  const renderLocationStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Select Polling Location</Text>
      <Text style={styles.stepDescription}>Choose the district, constituency, ward, and center you are observing</Text>

      {/* District Selection */}
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>District</Text>
        <TouchableOpacity
          style={styles.dropdownButton}
          onPress={() => setShowDistrictModal(true)}
        >
          <Text style={[styles.dropdownText, !formData.district && styles.placeholderText]}>
            {formData.district || 'Select district...'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Constituency Selection */}
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Constituency</Text>
        <TouchableOpacity
          style={[styles.dropdownButton, !formData.district && styles.disabledButton]}
          onPress={() => formData.district && setShowConstituencyModal(true)}
          disabled={!formData.district}
        >
          <Text style={[styles.dropdownText, !formData.constituency && styles.placeholderText]}>
            {formData.constituency || 'Select constituency...'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Ward Selection */}
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Ward</Text>
        <TouchableOpacity
          style={[styles.dropdownButton, !formData.constituency && styles.disabledButton]}
          onPress={() => formData.constituency && setShowWardModal(true)}
          disabled={!formData.constituency}
        >
          <Text style={[styles.dropdownText, !formData.ward && styles.placeholderText]}>
            {formData.ward || 'Select ward...'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Center Selection */}
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Polling Center</Text>
        <TouchableOpacity
          style={[styles.dropdownButton, !formData.ward && styles.disabledButton]}
          onPress={() => formData.ward && setShowCenterModal(true)}
          disabled={!formData.ward}
        >
          <Text style={[styles.dropdownText, !formData.center && styles.placeholderText]}>
            {formData.center || 'Select polling center...'}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderTimelineStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Select Timeline Period</Text>
      <Text style={styles.stepDescription}>Choose the time period you want to report on</Text>

      <View style={styles.timelineContainer}>
        {TIMELINE_PERIODS.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.timelineCard,
              selectedTimeline === period.id && styles.selectedTimelineCard
            ]}
            onPress={() => setSelectedTimeline(period.id)}
          >
            <View style={styles.timelineHeader}>
              <Text style={[
                styles.timelineTitle,
                selectedTimeline === period.id && styles.selectedTimelineTitle
              ]}>
                {period.label}
              </Text>
              <View style={[
                styles.radioButton,
                selectedTimeline === period.id && styles.selectedRadioButton
              ]}>
                {selectedTimeline === period.id && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
            </View>
            <Text style={[
              styles.timelineTime,
              selectedTimeline === period.id && styles.selectedTimelineTime
            ]}>
              {period.timeRange}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderQuestionsStep = () => {
    if (currentQuestions.length === 0) return null;

    const currentQuestion = currentQuestions[currentQuestionIndex];
    const timelinePeriod = TIMELINE_PERIODS.find(p => p.id === selectedTimeline);

    return (
      <View style={styles.stepContainer}>
        <View style={styles.questionHeader}>
          <Text style={styles.stepTitle}>{timelinePeriod?.label}</Text>
          <Text style={styles.questionProgress}>
            Question {currentQuestionIndex + 1} of {totalQuestions}
          </Text>
        </View>

        <View style={styles.locationSummary}>
          <Text style={styles.locationSummaryText}>
            {formData.center}, {formData.ward}, {formData.constituency}, {formData.district}
          </Text>
        </View>

        <QuestionComponent
          question={currentQuestion}
          value={formData[selectedTimeline as keyof FormData]?.[currentQuestion.id]}
          onChange={(value) => handleQuestionResponse(currentQuestion.id, value)}
        />
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Observer Checklist</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, {
            width: currentStep >= 2
              ? `${((currentQuestionIndex + 1) / totalQuestions) * 100}%`
              : `${((currentStep + 1) / 3) * 100}%`
          }]} />
        </View>
        <Text style={styles.progressText}>
          {currentStep >= 2
            ? `Question ${currentQuestionIndex + 1} of ${totalQuestions}`
            : `Step ${currentStep + 1} of 3`
          }
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {currentStep === 0 && renderLocationStep()}
        {currentStep === 1 && renderTimelineStep()}
        {currentStep >= 2 && renderQuestionsStep()}
      </ScrollView>

      {/* Navigation Buttons */}
      <View style={styles.navigationContainer}>
        {(currentStep > 0 || (currentStep >= 2 && currentQuestionIndex > 0)) && (
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={currentStep >= 2 ? handlePreviousQuestion : handlePreviousStep}
          >
            <Text style={styles.secondaryButtonText}>Previous</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[styles.primaryButton, currentStep === 0 && styles.fullWidthButton]}
          onPress={currentStep >= 2 ? handleNextQuestion : handleNextStep}
        >
          <Text style={styles.primaryButtonText}>
            {currentStep === 1
              ? 'Start Questions'
              : currentStep >= 2 && currentQuestionIndex === totalQuestions - 1
                ? 'Submit Checklist'
                : 'Next'
            }
          </Text>
        </TouchableOpacity>
      </View>

      {/* District Modal */}
      <Modal
        visible={showDistrictModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select District</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowDistrictModal(false)}
            >
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            {districts.map((district) => (
              <TouchableOpacity
                key={district.district_name}
                style={styles.modalItem}
                onPress={() => {
                  handleLocationSelection('district', district.district_name);
                  setShowDistrictModal(false);
                }}
              >
                <Text style={styles.modalItemText}>{district.district_name}</Text>
                {formData.district === district.district_name && (
                  <Ionicons name="checkmark" size={20} color="#3B82F6" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Constituency Modal */}
      <Modal
        visible={showConstituencyModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Constituency</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowConstituencyModal(false)}
            >
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            {availableConstituencies.map((constituency) => (
              <TouchableOpacity
                key={constituency.constituency_name}
                style={styles.modalItem}
                onPress={() => {
                  handleLocationSelection('constituency', constituency.constituency_name);
                  setShowConstituencyModal(false);
                }}
              >
                <Text style={styles.modalItemText}>{constituency.constituency_name}</Text>
                {formData.constituency === constituency.constituency_name && (
                  <Ionicons name="checkmark" size={20} color="#3B82F6" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Ward Modal */}
      <Modal
        visible={showWardModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Ward</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowWardModal(false)}
            >
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            {availableWards.map((ward) => (
              <TouchableOpacity
                key={ward.ward_name}
                style={styles.modalItem}
                onPress={() => {
                  handleLocationSelection('ward', ward.ward_name);
                  setShowWardModal(false);
                }}
              >
                <Text style={styles.modalItemText}>{ward.ward_name}</Text>
                {formData.ward === ward.ward_name && (
                  <Ionicons name="checkmark" size={20} color="#3B82F6" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Center Modal */}
      <Modal
        visible={showCenterModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Polling Center</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowCenterModal(false)}
            >
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            {availableCenters.map((center) => (
              <TouchableOpacity
                key={center.center_name}
                style={styles.modalItem}
                onPress={() => {
                  handleLocationSelection('center', center.center_name);
                  setShowCenterModal(false);
                }}
              >
                <Text style={styles.modalItemText}>{center.center_name}</Text>
                {formData.center === center.center_name && (
                  <Ionicons name="checkmark" size={20} color="#3B82F6" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  headerRight: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContainer: {
    paddingVertical: 24,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 32,
    lineHeight: 24,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  disabledButton: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
  },
  dropdownText: {
    fontSize: 16,
    color: '#111827',
    flex: 1,
  },
  placeholderText: {
    color: '#9CA3AF',
  },
  timelineContainer: {
    gap: 16,
  },
  timelineCard: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedTimelineCard: {
    borderColor: '#3B82F6',
    backgroundColor: '#EFF6FF',
  },
  timelineHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
  },
  selectedTimelineTitle: {
    color: '#1D4ED8',
  },
  timelineTime: {
    fontSize: 14,
    color: '#6B7280',
  },
  selectedTimelineTime: {
    color: '#3B82F6',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedRadioButton: {
    borderColor: '#3B82F6',
  },
  radioButtonInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3B82F6',
  },
  navigationContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullWidthButton: {
    flex: 1,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  modalCloseButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  modalItemText: {
    fontSize: 16,
    color: '#111827',
    flex: 1,
  },
  questionHeader: {
    marginBottom: 20,
  },
  questionProgress: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  locationSummary: {
    backgroundColor: '#EFF6FF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  locationSummaryText: {
    fontSize: 14,
    color: '#1D4ED8',
    fontWeight: '500',
    textAlign: 'center',
  },
});
