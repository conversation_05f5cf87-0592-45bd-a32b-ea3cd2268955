import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    FlatList,
    SafeAreaView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// Sample observer reports data
const SAMPLE_REPORTS = [
  {
    id: '1',
    date: '2024-12-15',
    time: '08:30 AM',
    timeline: 'Morning (6am - 10am)',
    district: 'Lilongwe',
    constituency: 'Lilongwe City Centre',
    ward: 'Area 3',
    center: 'Kamuzu Central Hospital',
    status: 'Completed',
    questionsAnswered: 43,
    totalQuestions: 43,
    submittedBy: '<PERSON>'
  },
  {
    id: '2',
    date: '2024-12-15',
    time: '02:15 PM',
    timeline: 'Afternoon (10am - 2pm)',
    district: 'Blantyre',
    constituency: 'Blantyre City South',
    ward: 'Ndirande',
    center: 'Ndirande Community Centre',
    status: 'Completed',
    questionsAnswered: 32,
    totalQuestions: 32,
    submittedBy: '<PERSON><PERSON>'
  },
  {
    id: '3',
    date: '2024-12-14',
    time: '06:45 PM',
    timeline: 'Evening (2pm - 4pm)',
    district: 'Mzuzu',
    constituency: 'Mzuzu City',
    ward: 'Chibanja',
    center: 'Chibanja Primary School',
    status: 'Completed',
    questionsAnswered: 29,
    totalQuestions: 29,
    submittedBy: 'Peter Mwale'
  },
  {
    id: '4',
    date: '2024-12-14',
    time: '11:20 PM',
    timeline: 'Vote Count (4pm - 12am)',
    district: 'Zomba',
    constituency: 'Zomba Central',
    ward: 'Matawale',
    center: 'Zomba Secondary School',
    status: 'Completed',
    questionsAnswered: 10,
    totalQuestions: 10,
    submittedBy: 'Grace Tembo'
  },
  {
    id: '5',
    date: '2024-12-13',
    time: '09:15 AM',
    timeline: 'Morning (6am - 10am)',
    district: 'Kasungu',
    constituency: 'Kasungu Central',
    ward: 'Kasungu Boma',
    center: 'Kasungu District Hospital',
    status: 'Draft',
    questionsAnswered: 25,
    totalQuestions: 43,
    submittedBy: 'James Nyirenda'
  },
  {
    id: '6',
    date: '2024-12-12',
    time: '03:30 PM',
    timeline: 'Afternoon (10am - 2pm)',
    district: 'Mangochi',
    constituency: 'Mangochi Central',
    ward: 'Mangochi Boma',
    center: 'Mangochi Community Hall',
    status: 'Completed',
    questionsAnswered: 32,
    totalQuestions: 32,
    submittedBy: 'Sarah Kachala'
  }
];

export default function MyObserverReportsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredReports, setFilteredReports] = useState(SAMPLE_REPORTS);
  const [selectedFilter, setSelectedFilter] = useState('All'); // All, Completed, Draft

  // Filter reports based on search query and status filter
  useEffect(() => {
    let filtered = SAMPLE_REPORTS;

    // Filter by status
    if (selectedFilter !== 'All') {
      filtered = filtered.filter(report => report.status === selectedFilter);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(report =>
        report.district.toLowerCase().includes(query) ||
        report.constituency.toLowerCase().includes(query) ||
        report.ward.toLowerCase().includes(query) ||
        report.center.toLowerCase().includes(query) ||
        report.timeline.toLowerCase().includes(query) ||
        report.submittedBy.toLowerCase().includes(query)
      );
    }

    setFilteredReports(filtered);
  }, [searchQuery, selectedFilter]);

  const handleReportPress = (report: any) => {
    Alert.alert(
      'Report Details',
      `View details for ${report.center} report from ${report.date}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'View Details', onPress: () => {
          // Navigate to report details (to be implemented)
          Alert.alert('Feature', 'Report details view will be implemented soon!');
        }}
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return '#10B981';
      case 'Draft':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  const getStatusBackgroundColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return '#DCFCE7';
      case 'Draft':
        return '#FEF3C7';
      default:
        return '#F3F4F6';
    }
  };

  const renderReportItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.reportCard}
      onPress={() => handleReportPress(item)}
    >
      <View style={styles.reportHeader}>
        <View style={styles.reportTitleContainer}>
          <Text style={styles.reportTitle}>{item.center}</Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusBackgroundColor(item.status) }
          ]}>
            <Text style={[
              styles.statusText,
              { color: getStatusColor(item.status) }
            ]}>
              {item.status}
            </Text>
          </View>
        </View>
        <Text style={styles.reportDate}>{item.date} at {item.time}</Text>
      </View>

      <View style={styles.reportDetails}>
        <View style={styles.locationInfo}>
          <Ionicons name="location-outline" size={16} color="#6B7280" />
          <Text style={styles.locationText}>
            {item.ward}, {item.constituency}, {item.district}
          </Text>
        </View>

        <View style={styles.timelineInfo}>
          <Ionicons name="time-outline" size={16} color="#6B7280" />
          <Text style={styles.timelineText}>{item.timeline}</Text>
        </View>

        <View style={styles.progressInfo}>
          <Ionicons name="checkmark-circle-outline" size={16} color="#6B7280" />
          <Text style={styles.progressText}>
            {item.questionsAnswered}/{item.totalQuestions} questions answered
          </Text>
        </View>

        <View style={styles.submitterInfo}>
          <Ionicons name="person-outline" size={16} color="#6B7280" />
          <Text style={styles.submitterText}>Submitted by {item.submittedBy}</Text>
        </View>
      </View>

      <View style={styles.reportFooter}>
        <TouchableOpacity style={styles.viewButton}>
          <Text style={styles.viewButtonText}>View Details</Text>
          <Ionicons name="chevron-forward" size={16} color="#3B82F6" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderFilterButton = (filter: string) => (
    <TouchableOpacity
      key={filter}
      style={[
        styles.filterButton,
        selectedFilter === filter && styles.activeFilterButton
      ]}
      onPress={() => setSelectedFilter(filter)}
    >
      <Text style={[
        styles.filterButtonText,
        selectedFilter === filter && styles.activeFilterButtonText
      ]}>
        {filter}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Observer Reports</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/observer-checklist')}
        >
          <Ionicons name="add" size={24} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color="#9CA3AF" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by location, timeline, or observer..."
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
            >
              <Ionicons name="close-circle" size={20} color="#9CA3AF" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {['All', 'Completed', 'Draft'].map(renderFilterButton)}
      </View>

      {/* Reports List */}
      <View style={styles.listContainer}>
        <Text style={styles.resultsText}>
          {filteredReports.length} report{filteredReports.length !== 1 ? 's' : ''} found
        </Text>
        
        <FlatList
          data={filteredReports}
          renderItem={renderReportItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="document-text-outline" size={64} color="#D1D5DB" />
              <Text style={styles.emptyTitle}>No reports found</Text>
              <Text style={styles.emptyDescription}>
                {searchQuery ? 'Try adjusting your search terms' : 'Start by creating your first observer report'}
              </Text>
            </View>
          }
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  addButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#EFF6FF',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    marginLeft: 12,
  },
  clearButton: {
    padding: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeFilterButton: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeFilterButtonText: {
    color: '#FFFFFF',
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  resultsText: {
    fontSize: 14,
    color: '#6B7280',
    paddingVertical: 12,
  },
  listContent: {
    paddingBottom: 20,
  },
  reportCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  reportHeader: {
    marginBottom: 12,
  },
  reportTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  reportDate: {
    fontSize: 14,
    color: '#6B7280',
  },
  reportDetails: {
    gap: 8,
    marginBottom: 12,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
  },
  timelineInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timelineText: {
    fontSize: 14,
    color: '#374151',
  },
  progressInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressText: {
    fontSize: 14,
    color: '#374151',
  },
  submitterInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  submitterText: {
    fontSize: 14,
    color: '#374151',
  },
  reportFooter: {
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingTop: 12,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  viewButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#3B82F6',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});
