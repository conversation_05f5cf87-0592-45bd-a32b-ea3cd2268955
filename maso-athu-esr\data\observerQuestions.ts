// Observer Checklist Questions Data
export const OBSERVER_QUESTIONS = {
  morning: [
    {
      id: '1a',
      text: 'Did the Polling Station open on time?',
      type: 'yesno',
      hasTextInput: true // Show text input if "No" is selected
    },
    {
      id: '1b',
      text: 'Was there a long voting queue/line when the Polling Station opened?',
      type: 'yesno'
    },
    {
      id: '2a',
      text: 'Total number of Electoral Commission Officials present',
      type: 'number'
    },
    {
      id: '2b',
      text: 'Total number of security personnel present',
      type: 'number'
    },
    {
      id: '2c',
      text: 'Are there Official Political Party Representatives present?',
      type: 'politicalparty'
    },
    {
      id: '3a',
      text: 'Security personnel have been able to maintain order at the Polling Station',
      type: 'yesno'
    },
    {
      id: '3b',
      text: 'The location of the Polling Station was in a safe and convenient location for the women.',
      type: 'yesno'
    },
    {
      id: '4a',
      text: 'The ballot paper packages were sealed and was opened in the presence of all Officials',
      type: 'yesno'
    },
    {
      id: '4b',
      text: 'Number of ballot papers on opening station',
      type: 'number'
    },
    {
      id: '4c',
      text: 'Number of ballot boxes on opening station',
      type: 'number'
    },
    {
      id: '4d',
      text: 'Number of embossing devices on opening station',
      type: 'number'
    },
    {
      id: '4e',
      text: 'Number of Ink bottles for marking fingers on opening station',
      type: 'number'
    },
    {
      id: '4f',
      text: 'Number of pens on opening station',
      type: 'number'
    },
    {
      id: '4g',
      text: 'How many Voters List/Register were available at the Polling Station at the time of opening?',
      type: 'number'
    },
    {
      id: '4h',
      text: 'Have you witnessed or heard of any malfunctions of polling equipment or shortage of voting materials that caused voting to be negatively affected? How long did it take to resolve the issue?',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '5a',
      text: 'Voters were asked to show their Voter Registration Certificates before voting?',
      type: 'yesno'
    },
    {
      id: '5b',
      text: 'Voters without Voter Registration Certificates were not allowed to vote.',
      type: 'yesno'
    },
    {
      id: '5c',
      text: 'Voters with Voter Registration Certificate from other Polling Stations were allowed to vote.',
      type: 'yesno'
    },
    {
      id: '5d',
      text: 'Voters names were ticked off the Voter Register/List once they were identified?',
      type: 'yesno'
    },
    {
      id: '5e',
      text: 'Voters names were called out loud enough for all monitors to hear?',
      type: 'yesno'
    },
    {
      id: '5f',
      text: 'How many people came without voter certificates?',
      type: 'number'
    },
    {
      id: '5g',
      text: 'How many people were sent back from the polling station?',
      type: 'number'
    },
    {
      id: '6a',
      text: 'The voting procedures were explained to voters by MEC Officials.',
      type: 'yesno'
    },
    {
      id: '6b',
      text: 'Voters were given privacy to cast their ballots after pointing out the correct boxes to them.',
      type: 'yesno'
    },
    {
      id: '6c',
      text: 'Party representatives were campaigning at or near Polling Station',
      type: 'yesno'
    },
    {
      id: '6d',
      text: 'Party representatives were soliciting to buy votes at Polling Station location.',
      type: 'yesno'
    },
    {
      id: '6e',
      text: 'Voters Registration Certificate cards were embossed after voting?',
      type: 'yesno'
    },
    {
      id: '6f',
      text: 'Number of people attempting to vote twice?',
      type: 'number'
    },
    {
      id: '6g',
      text: 'Voters who made mistakes were given new ballot papers',
      type: 'yesno'
    },
    {
      id: '6h',
      text: 'Voters surrendered spoilt ballot papers after requesting new ones',
      type: 'yesno'
    },
    {
      id: '6i',
      text: 'Voters index fingers were checked for ink before voting',
      type: 'yesno'
    },
    {
      id: '6j',
      text: 'Voters index fingers inked on voting',
      type: 'yesno'
    },
    {
      id: '7a',
      text: 'The voting queue/line was moving along during the Morning hours (6:00am to 10:00am)?',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '7b',
      text: 'The Polling Station was spacious and adequate for Voters\' movement and voting process.',
      type: 'yesno'
    },
    {
      id: '7c',
      text: 'The polling station was shaded and protected from the sun and rain.',
      type: 'yesno'
    },
    {
      id: '7d',
      text: 'The polling station implemented measures with respect to protecting voters from any health outbreaks at the time',
      type: 'yesno'
    },
    {
      id: '8a',
      text: 'Persons with disabilities were given priority & easy access to vote',
      type: 'yesno'
    },
    {
      id: '8b',
      text: 'Pregnant women were given priority and easy access to vote.',
      type: 'yesno'
    },
    {
      id: '8c',
      text: 'Women with children under 5 were given priority and easy access to vote',
      type: 'yesno'
    },
    {
      id: '8d',
      text: 'The elderly were given priority and easy access to vote?',
      type: 'yesno'
    },
    {
      id: '9a',
      text: 'Party observers were able to fully observe the voting process and perform their duties',
      type: 'yesno'
    },
    {
      id: '9b',
      text: 'Various CSO observers were able to fully observe the voting process and perform their duties',
      type: 'yesno'
    },
    {
      id: '9c',
      text: 'Did you witness or hear of any complaints lodged at the polling station by observers and or anyone else',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '9d',
      text: 'How many complaints were lodged',
      type: 'number'
    }
  ],
  afternoon: [
    {
      id: '10a',
      text: 'Was the polling station still open?',
      type: 'yesno'
    },
    {
      id: '10b',
      text: 'Was there a long voting queue/line in the afternoon hours?',
      type: 'yesno'
    },
    {
      id: '11a',
      text: 'Total number of Electoral Commission Officials present',
      type: 'number'
    },
    {
      id: '11b',
      text: 'Total number of security personnel present',
      type: 'number'
    },
    {
      id: '11c',
      text: 'Are there Official Political Party Representatives present?',
      type: 'politicalparty'
    },
    {
      id: '12a',
      text: 'Security personnel have been able to maintain order at the Polling Station',
      type: 'yesno'
    },
    {
      id: '12b',
      text: 'The location of the Polling Station was in a safe and convenient location for the women.',
      type: 'yesno'
    },
    {
      id: '13a',
      text: 'Number of additional ballot boxes received at polling station',
      type: 'number'
    },
    {
      id: '13b',
      text: 'Number of embossing devices at Polling Station',
      type: 'number'
    },
    {
      id: '13c',
      text: 'Number of Ink bottles for marking fingers at Polling Station',
      type: 'number'
    },
    {
      id: '14a',
      text: 'Have you witnessed or heard of any malfunctions of polling equipment or shortage of voting materials that caused voting to be negatively affected? How long did it take to resolve the issue?',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '15a',
      text: 'Voters were asked to show their Voter Registration Certificates before voting?',
      type: 'yesno'
    },
    {
      id: '15b',
      text: 'Voters without Voter Registration Certificates were not allowed to vote.',
      type: 'yesno'
    },
    {
      id: '15c',
      text: 'Voters with Voter Registration Certificate from other Polling Stations were allowed to vote.',
      type: 'yesno'
    },
    {
      id: '15d',
      text: 'Voters names were ticked off the Voter Register/List once they were identified?',
      type: 'yesno'
    },
    {
      id: '15e',
      text: 'Voters names were called out loud enough for all monitors to hear?',
      type: 'yesno'
    },
    {
      id: '15f',
      text: 'How many people came without voter certificates?',
      type: 'number'
    },
    {
      id: '15g',
      text: 'How many people were sent back from the polling station?',
      type: 'number'
    },
    {
      id: '16a',
      text: 'The voting procedures were explained to voters by MEC Officials.',
      type: 'yesno'
    },
    {
      id: '16b',
      text: 'Voters were given privacy to cast their ballots after pointing out the correct boxes to them.',
      type: 'yesno'
    },
    {
      id: '16c',
      text: 'Voters Registration Certificate cards were embossed after voting?',
      type: 'yesno'
    },
    {
      id: '16d',
      text: 'Number of people attempting to vote twice?',
      type: 'number'
    },
    {
      id: '16e',
      text: 'Voters who made mistakes were given new ballot papers',
      type: 'yesno'
    },
    {
      id: '16f',
      text: 'Voters surrendered spoilt ballot papers after requesting new ones',
      type: 'yesno'
    },
    {
      id: '16g',
      text: 'Were voters index fingers checked for ink before voting',
      type: 'yesno'
    },
    {
      id: '16h',
      text: 'Were voters index fingers inked on voting',
      type: 'yesno'
    },
    {
      id: '17a',
      text: 'The voting queue/line was moving along during the Afternoon hours (10:00am to 02:00pm)?',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '17b',
      text: 'The Polling Station was spacious and adequate for Voters\' movement and voting process.',
      type: 'yesno'
    },
    {
      id: '17c',
      text: 'The polling station was shaded and protected from the sun and rain.',
      type: 'yesno'
    },
    {
      id: '17d',
      text: 'The polling station implemented measures with respect to protecting voters from any health outbreaks at the time',
      type: 'yesno'
    },
    {
      id: '18a',
      text: 'Persons with disabilities were given priority & easy access to vote',
      type: 'yesno'
    },
    {
      id: '18b',
      text: 'Pregnant women were given priority and easy access to vote.',
      type: 'yesno'
    },
    {
      id: '18c',
      text: 'Women with children under 5 were given priority and easy access to vote',
      type: 'yesno'
    },
    {
      id: '18d',
      text: 'The elderly were given priority and easy access to vote?',
      type: 'yesno'
    },
    {
      id: '19a',
      text: 'Party observers were able to fully observe the voting process and perform their duties',
      type: 'yesno'
    },
    {
      id: '19b',
      text: 'Various CSO observers were able to fully observe the voting process and perform their duties',
      type: 'yesno'
    },
    {
      id: '19c',
      text: 'Did you witness or hear of any complaints lodged at the polling station by observers and or anyone else',
      type: 'yesno'
    },
    {
      id: '19d',
      text: 'How many complaints were lodged',
      type: 'number'
    },
    {
      id: '20a',
      text: 'Have you witnessed or heard any persons or people who have been ferried from another area to vote at the Centre you observed?',
      type: 'yesno'
    }
  ],
  evening: [
    {
      id: '21a',
      text: 'Was the polling station still open?',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '21b',
      text: 'Was there a long voting queue/line in the afternoon hours?',
      type: 'yesno'
    },
    {
      id: '22a',
      text: 'Total number of Electoral Commission Officials present',
      type: 'number'
    },
    {
      id: '22b',
      text: 'Total number of security personnel present',
      type: 'number'
    },
    {
      id: '22c',
      text: 'Are there Official Political Party Representatives present?',
      type: 'politicalparty'
    },
    {
      id: '23a',
      text: 'Security personnel have been able to maintain order at the Polling Station',
      type: 'yesno'
    },
    {
      id: '24a',
      text: 'Number of ballot papers at Polling Station',
      type: 'number'
    },
    {
      id: '24b',
      text: 'Number of additional ballot boxes received at the polling station',
      type: 'number'
    },
    {
      id: '24c',
      text: 'Number of embossing devices at Polling Station',
      type: 'number'
    },
    {
      id: '24d',
      text: 'Number of Ink bottles for marking fingers at Polling Station',
      type: 'number'
    },
    {
      id: '24e',
      text: 'Have you witnessed or heard of any malfunctions of polling equipment or shortage of voting materials that caused voting to be negatively affected? How long did it take to resolve the issue?',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '25a',
      text: 'Voters were asked to show their Voter Registration Certificates before voting?',
      type: 'yesno'
    },
    {
      id: '25b',
      text: 'Voters without Voter Registration Certificates were not allowed to vote.',
      type: 'yesno'
    },
    {
      id: '25c',
      text: 'Voters with Voter Registration Certificate from other Polling Stations were allowed to vote.',
      type: 'yesno'
    },
    {
      id: '25d',
      text: 'Voters names were ticked off the Voter Register/List once they were identified?',
      type: 'yesno'
    },
    {
      id: '25e',
      text: 'Voters names were called out loud enough for all monitors to hear?',
      type: 'yesno'
    },
    {
      id: '25f',
      text: 'How many people came without voter certificates?',
      type: 'number'
    },
    {
      id: '25g',
      text: 'How many people were sent back from the polling station?',
      type: 'number'
    },
    {
      id: '26a',
      text: 'The voting procedures were explained to voters by MEC Officials.',
      type: 'yesno'
    },
    {
      id: '26b',
      text: 'Voters were given privacy to cast their ballots after pointing out the correct boxes to them.',
      type: 'yesno'
    },
    {
      id: '26c',
      text: 'Voters Registration Certificate cards were embossed after voting?',
      type: 'yesno'
    },
    {
      id: '26d',
      text: 'Number of people attempting to vote twice?',
      type: 'number'
    },
    {
      id: '26e',
      text: 'Voters who made mistakes were given new ballot papers',
      type: 'yesno'
    },
    {
      id: '26f',
      text: 'Voters surrendered spoilt ballot papers after requesting new ones',
      type: 'yesno'
    },
    {
      id: '26g',
      text: 'Were voters index fingers checked for ink before voting',
      type: 'yesno'
    },
    {
      id: '26h',
      text: 'Were voters index fingers inked on voting',
      type: 'yesno'
    },
    {
      id: '27a',
      text: 'The voting queue/line was moving along during the Evening hours (2:00pm to 4:00pm)?',
      type: 'yesno',
      hasTextInput: true
    },
    {
      id: '27b',
      text: 'The Polling Station was spacious and adequate for Voters\' movement and voting process.',
      type: 'yesno'
    },
    {
      id: '27c',
      text: 'The polling station was shaded and protected from the sun and rain.',
      type: 'yesno'
    },
    {
      id: '27d',
      text: 'The polling station implemented measures with respect to Covid-19 prevention',
      type: 'yesno'
    },
    {
      id: '28a',
      text: 'Persons with disabilities were given priority & easy access to vote',
      type: 'yesno'
    },
    {
      id: '28b',
      text: 'Pregnant women were given priority and easy access to vote.',
      type: 'yesno'
    },
    {
      id: '28c',
      text: 'The elderly were given priority and easy access to vote?',
      type: 'yesno'
    },
    {
      id: '29a',
      text: 'Party observers were able to fully observe the voting process and perform their duties',
      type: 'yesno'
    },
    {
      id: '29b',
      text: 'Various CSO observers were able to fully observe the voting process and perform their duties',
      type: 'yesno'
    },
    {
      id: '29c',
      text: 'Did you witness or hear of any complaints lodged at the polling station by observers and or anyone else',
      type: 'yesno'
    },
    {
      id: '29d',
      text: 'How many complaints were lodged',
      type: 'number'
    }
  ],
  votecount: [
    {
      id: '30a',
      text: 'What time did the Polling Station close?',
      type: 'time'
    },
    {
      id: '31a',
      text: 'Were ballot papers counted at the polling station?',
      type: 'yesno'
    },
    {
      id: '31b',
      text: 'Was there adequate light for ballot counting at the polling station?',
      type: 'yesno'
    },
    {
      id: '32a',
      text: 'The ballot papers were transported somewhere else for counting?',
      type: 'yesno'
    },
    {
      id: '32b',
      text: 'Number of unused ballot papers at the polling station',
      type: 'number'
    },
    {
      id: '32c',
      text: 'Number of spoilt ballot papers at the polling stations',
      type: 'number'
    },
    {
      id: '32d',
      text: 'Number of null and void votes at the polling station',
      type: 'number'
    },
    {
      id: '32e',
      text: 'All party representatives were present at vote counting',
      type: 'yesno'
    },
    {
      id: '32f',
      text: 'All observers were present and performed their duty at vote counting',
      type: 'yesno'
    },
    {
      id: '32g',
      text: 'You were able to capture the data of votes counted and submitted on Maso Athu',
      type: 'yesno'
    }
  ]
};
