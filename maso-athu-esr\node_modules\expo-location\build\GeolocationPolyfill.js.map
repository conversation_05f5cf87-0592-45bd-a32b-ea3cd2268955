{"version": 3, "file": "GeolocationPolyfill.js", "sourceRoot": "", "sources": ["../src/GeolocationPolyfill.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAkB,gBAAgB,EAAmB,MAAM,kBAAkB,CAAC;AACrF,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAW3D,cAAc;AACd;;GAEG;AACH,MAAM,UAAU,6BAA6B;IAC3C,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,+DAA+D;QAC/D,IAAI,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,EAAE,CAAC;YAC1B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;QAC/B,CAAC;QAED,aAAa;QACb,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG;YAC7B,kBAAkB;YAClB,aAAa;YACb,UAAU;SACX,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAS,yBAAyB,CAAC,OAA2B;IAC5D,OAAO;QACL,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ;KACzF,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAmC,EACnC,QAAkC,GAAG,EAAE,GAAE,CAAC,EAC1C,UAA8B,EAAE;IAEhC,+BAA+B,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC;AAED,mGAAmG;AACnG,0DAA0D;AAC1D,KAAK,UAAU,+BAA+B,CAC5C,OAAmC,EACnC,KAA+B,EAC/B,OAA2B;IAE3B,IAAI,CAAC;QACH,MAAM,YAAY,CAAC,uBAAuB,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,uBAAuB,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9F,OAAO,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,KAAK,CAAC,CAAC,CAAC,CAAC;IACX,CAAC;AACH,CAAC;AAED,gDAAgD;AAChD,SAAS,aAAa,CACpB,OAAmC,EACnC,KAA+B,EAC/B,OAA2B;IAE3B,MAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE7D,YAAY,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE;QACvE,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,6CAA6C;AAC7C,SAAS,UAAU,CAAC,OAAe;IACjC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AACjD,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport ExpoLocation from './ExpoLocation';\nimport { LocationObject, LocationAccuracy, LocationOptions } from './Location.types';\nimport { LocationSubscriber } from './LocationSubscribers';\n\ntype GeolocationSuccessCallback = (data: LocationObject) => void;\ntype GeolocationErrorCallback = (error: any) => void;\n\ntype GeolocationOptions = {\n  enableHighAccuracy?: boolean;\n};\n\ndeclare const global: any;\n\n// @needsAudit\n/**\n * Polyfills `navigator.geolocation` for interop with the core React Native and Web API approach to geolocation.\n */\nexport function installWebGeolocationPolyfill(): void {\n  if (Platform.OS !== 'web') {\n    // Make sure `window.navigator` is defined in the global scope.\n    if (!('window' in global)) {\n      global.window = global;\n    }\n    if (!('navigator' in global.window)) {\n      global.window.navigator = {};\n    }\n\n    // @ts-ignore\n    window.navigator.geolocation = {\n      getCurrentPosition,\n      watchPosition,\n      clearWatch,\n    };\n  }\n}\n\nfunction convertGeolocationOptions(options: GeolocationOptions): LocationOptions {\n  return {\n    accuracy: options.enableHighAccuracy ? LocationAccuracy.High : LocationAccuracy.Balanced,\n  };\n}\n\nfunction getCurrentPosition(\n  success: GeolocationSuccessCallback,\n  error: GeolocationErrorCallback = () => {},\n  options: GeolocationOptions = {}\n): void {\n  _getCurrentPositionAsyncWrapper(success, error, options);\n}\n\n// This function exists to let us continue to return undefined from getCurrentPosition, while still\n// using async/await for the internal implementation of it\nasync function _getCurrentPositionAsyncWrapper(\n  success: GeolocationSuccessCallback,\n  error: GeolocationErrorCallback,\n  options: GeolocationOptions\n): Promise<any> {\n  try {\n    await ExpoLocation.requestPermissionsAsync();\n    const result = await ExpoLocation.getCurrentPositionAsync(convertGeolocationOptions(options));\n    success(result);\n  } catch (e) {\n    error(e);\n  }\n}\n\n// Polyfill: navigator.geolocation.watchPosition\nfunction watchPosition(\n  success: GeolocationSuccessCallback,\n  error: GeolocationErrorCallback,\n  options: GeolocationOptions\n) {\n  const watchId = LocationSubscriber.registerCallback(success);\n\n  ExpoLocation.watchPositionImplAsync(watchId, options).catch((err: any) => {\n    LocationSubscriber.unregisterCallback(watchId);\n    error({ watchId, message: err.message, code: err.code });\n  });\n\n  return watchId;\n}\n\n// Polyfill: navigator.geolocation.clearWatch\nfunction clearWatch(watchId: number) {\n  LocationSubscriber.unregisterCallback(watchId);\n}\n"]}