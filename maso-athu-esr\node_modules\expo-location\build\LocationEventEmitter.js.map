{"version": 3, "file": "LocationEventEmitter.js", "sourceRoot": "", "sources": ["../src/LocationEventEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAEvD,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAE1C,MAAM,CAAC,MAAM,oBAAoB,GAAG,IAAI,kBAAkB,CAAC,YAAY,CAAC,CAAC", "sourcesContent": ["import { LegacyEventEmitter } from 'expo-modules-core';\n\nimport ExpoLocation from './ExpoLocation';\n\nexport const LocationEventEmitter = new LegacyEventEmitter(ExpoLocation);\n"]}