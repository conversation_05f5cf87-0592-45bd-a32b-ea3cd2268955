# Database Schema & API Specifications

## Database Tables

### 1. Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    role VARCHAR(20) DEFAULT 'observer', -- observer, admin, moderator
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Incidents Table
```sql
CREATE TABLE incidents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    title_chichewa VARCHAR(200), -- "lemban chachitika mwachindunji"
    description TEXT NOT NULL,
    description_chichewa TEXT, -- "long<PERSON>lani chachitikacho mwasatanesatane"
    incident_type_id INTEGER NOT NULL, -- Main category (61-65)
    incident_subtype_id INTEGER NOT NULL, -- Subcategory
    incident_date DATE NOT NULL,
    district VARCHAR(100) NOT NULL,
    location VARCHAR(200) NOT NULL,
    latitude DECIMAL(10, 8), -- GPS coordinates
    longitude DECIMAL(11, 8), -- GPS coordinates
    status VARCHAR(20) DEFAULT 'submitted', -- submitted, under_review, resolved, closed
    priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, critical
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Incident Types Table
```sql
CREATE TABLE incident_types (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_chichewa VARCHAR(100),
    description TEXT,
    color VARCHAR(7) DEFAULT '#6B7280' -- Hex color for UI
);

-- Insert main categories
INSERT INTO incident_types (id, name, name_chichewa, color) VALUES
(61, 'Security', 'Chitetezo', '#EF4444'),
(62, 'Health and Environment', 'Thanzi ndi Chilengedwe', '#10B981'),
(63, 'Gender', 'Kusiyana kwa Amuna ndi Akazi', '#8B5CF6'),
(64, 'Governance and Human Rights Violations', 'Ulamuliro ndi Kuphwanya Ufulu wa Anthu', '#F59E0B'),
(65, 'Elections', 'Zisankho', '#3B82F6');
```

### 4. Incident Subtypes Table
```sql
CREATE TABLE incident_subtypes (
    id INTEGER PRIMARY KEY,
    parent_type_id INTEGER REFERENCES incident_types(id),
    name VARCHAR(100) NOT NULL,
    name_chichewa VARCHAR(100),
    code VARCHAR(10) NOT NULL -- e.g., "61.1", "61.2"
);
```

### 5. Incident Attachments Table
```sql
CREATE TABLE incident_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    incident_id UUID REFERENCES incidents(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- image/jpeg, video/mp4, etc.
    file_size INTEGER NOT NULL, -- in bytes
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6. Observer Checklists Table
```sql
CREATE TABLE observer_checklists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    district VARCHAR(100) NOT NULL,
    constituency VARCHAR(100) NOT NULL,
    ward VARCHAR(100) NOT NULL,
    polling_center VARCHAR(200) NOT NULL,
    timeline_period VARCHAR(20) NOT NULL, -- morning, afternoon, evening, votecount
    status VARCHAR(20) DEFAULT 'draft', -- draft, completed, submitted
    responses JSONB NOT NULL, -- Store all question responses
    questions_answered INTEGER DEFAULT 0,
    total_questions INTEGER NOT NULL,
    submitted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. Polling Centers Table (Reference Data)
```sql
CREATE TABLE polling_centers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    center_name VARCHAR(200) NOT NULL,
    center_code VARCHAR(50),
    ward VARCHAR(100) NOT NULL,
    constituency VARCHAR(100) NOT NULL,
    district VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_active BOOLEAN DEFAULT true
);
```

### 8. Audit Log Table
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- CREATE, UPDATE, DELETE, VIEW
    table_name VARCHAR(50) NOT NULL,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Indexes for Performance
```sql
-- Incidents indexes
CREATE INDEX idx_incidents_user_id ON incidents(user_id);
CREATE INDEX idx_incidents_date ON incidents(incident_date);
CREATE INDEX idx_incidents_district ON incidents(district);
CREATE INDEX idx_incidents_type ON incidents(incident_type_id);
CREATE INDEX idx_incidents_status ON incidents(status);
CREATE INDEX idx_incidents_created_at ON incidents(created_at);

-- Observer checklists indexes
CREATE INDEX idx_checklists_user_id ON observer_checklists(user_id);
CREATE INDEX idx_checklists_district ON observer_checklists(district);
CREATE INDEX idx_checklists_timeline ON observer_checklists(timeline_period);
CREATE INDEX idx_checklists_status ON observer_checklists(status);
CREATE INDEX idx_checklists_created_at ON observer_checklists(created_at);

-- Polling centers indexes
CREATE INDEX idx_polling_centers_district ON polling_centers(district);
CREATE INDEX idx_polling_centers_constituency ON polling_centers(constituency);
CREATE INDEX idx_polling_centers_ward ON polling_centers(ward);
```
