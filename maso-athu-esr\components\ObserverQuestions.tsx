import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Modal,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// Political parties for dropdown
const POLITICAL_PARTIES = [
  'Malawi Congress Party (MCP)',
  'Democratic Progressive Party (DPP)',
  'United Transformation Movement (UTM)',
  'Alliance for Democracy (AFORD)',
  'People\'s Party (PP)',
  'United Democratic Front (UDF)',
  'Malawi Forum for Unity and Development (MAFUNDE)',
  'Republican Party (RP)',
  'New Labour Party (NLP)',
  'Petra Party',
  'Other'
];

interface PoliticalPartyData {
  party: string;
  count: number;
}

interface QuestionProps {
  question: any;
  value: any;
  onChange: (value: any) => void;
}

const QuestionComponent: React.FC<QuestionProps> = ({ question, value, onChange }) => {
  const [showPartyModal, setShowPartyModal] = useState(false);
  const [politicalParties, setPoliticalParties] = useState<PoliticalPartyData[]>(value?.politicalParties || []);

  const handleYesNoChange = (answer: boolean) => {
    if (question.hasTextInput && answer === false) {
      onChange({ answer, textInput: '' });
    } else {
      onChange({ answer });
    }
  };

  const handleTextInputChange = (text: string) => {
    onChange({ ...value, textInput: text });
  };

  const handleNumberChange = (text: string) => {
    const number = parseInt(text) || 0;
    onChange(number);
  };

  const addPoliticalParty = (party: string) => {
    const newParty = { party, count: 0 };
    const updatedParties = [...politicalParties, newParty];
    setPoliticalParties(updatedParties);
    onChange({ ...value, politicalParties: updatedParties });
    setShowPartyModal(false);
  };

  const updatePartyCount = (index: number, count: number) => {
    const updatedParties = [...politicalParties];
    updatedParties[index].count = count;
    setPoliticalParties(updatedParties);
    onChange({ ...value, politicalParties: updatedParties });
  };

  const removeParty = (index: number) => {
    const updatedParties = politicalParties.filter((_, i) => i !== index);
    setPoliticalParties(updatedParties);
    onChange({ ...value, politicalParties: updatedParties });
  };

  const renderYesNoQuestion = () => (
    <View style={styles.responseContainer}>
      <View style={styles.yesNoContainer}>
        <TouchableOpacity
          style={[
            styles.yesNoButton,
            value?.answer === true && styles.selectedYesButton
          ]}
          onPress={() => handleYesNoChange(true)}
        >
          <Text style={[
            styles.yesNoText,
            value?.answer === true && styles.selectedYesText
          ]}>
            Yes
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.yesNoButton,
            value?.answer === false && styles.selectedNoButton
          ]}
          onPress={() => handleYesNoChange(false)}
        >
          <Text style={[
            styles.yesNoText,
            value?.answer === false && styles.selectedNoText
          ]}>
            No
          </Text>
        </TouchableOpacity>
      </View>
      
      {question.hasTextInput && value?.answer === false && (
        <View style={styles.textInputContainer}>
          <Text style={styles.textInputLabel}>Please explain:</Text>
          <TextInput
            style={styles.textInput}
            value={value?.textInput || ''}
            onChangeText={handleTextInputChange}
            placeholder="Enter explanation..."
            placeholderTextColor="#9CA3AF"
            multiline
            numberOfLines={3}
          />
        </View>
      )}
    </View>
  );

  const renderNumberQuestion = () => (
    <View style={styles.responseContainer}>
      <TextInput
        style={styles.numberInput}
        value={value?.toString() || ''}
        onChangeText={handleNumberChange}
        placeholder="Enter number..."
        placeholderTextColor="#9CA3AF"
        keyboardType="numeric"
      />
    </View>
  );

  const renderTextQuestion = () => (
    <View style={styles.responseContainer}>
      <TextInput
        style={styles.textInput}
        value={value || ''}
        onChangeText={onChange}
        placeholder="Enter your response..."
        placeholderTextColor="#9CA3AF"
        multiline
        numberOfLines={3}
      />
    </View>
  );

  const renderTimeQuestion = () => (
    <View style={styles.responseContainer}>
      <TextInput
        style={styles.numberInput}
        value={value || ''}
        onChangeText={onChange}
        placeholder="HH:MM (e.g., 16:30)"
        placeholderTextColor="#9CA3AF"
      />
    </View>
  );

  const renderPoliticalPartyQuestion = () => (
    <View style={styles.responseContainer}>
      <View style={styles.yesNoContainer}>
        <TouchableOpacity
          style={[
            styles.yesNoButton,
            value?.hasParties === true && styles.selectedYesButton
          ]}
          onPress={() => onChange({ ...value, hasParties: true, politicalParties: politicalParties })}
        >
          <Text style={[
            styles.yesNoText,
            value?.hasParties === true && styles.selectedYesText
          ]}>
            Yes
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.yesNoButton,
            value?.hasParties === false && styles.selectedNoButton
          ]}
          onPress={() => onChange({ hasParties: false, politicalParties: [] })}
        >
          <Text style={[
            styles.yesNoText,
            value?.hasParties === false && styles.selectedNoText
          ]}>
            No
          </Text>
        </TouchableOpacity>
      </View>

      {value?.hasParties === true && (
        <View style={styles.politicalPartiesContainer}>
          <Text style={styles.partiesLabel}>Political Party Representatives:</Text>
          
          {politicalParties.map((party, index) => (
            <View key={index} style={styles.partyItem}>
              <View style={styles.partyHeader}>
                <Text style={styles.partyName}>{party.party}</Text>
                <TouchableOpacity
                  style={styles.removePartyButton}
                  onPress={() => removeParty(index)}
                >
                  <Ionicons name="close-circle" size={20} color="#EF4444" />
                </TouchableOpacity>
              </View>
              <View style={styles.partyCountContainer}>
                <Text style={styles.partyCountLabel}>Number of representatives:</Text>
                <TextInput
                  style={styles.partyCountInput}
                  value={party.count.toString()}
                  onChangeText={(text) => updatePartyCount(index, parseInt(text) || 0)}
                  placeholder="0"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>
            </View>
          ))}

          <TouchableOpacity
            style={styles.addPartyButton}
            onPress={() => setShowPartyModal(true)}
          >
            <Ionicons name="add-circle-outline" size={20} color="#3B82F6" />
            <Text style={styles.addPartyText}>Add Political Party</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Political Party Selection Modal */}
      <Modal
        visible={showPartyModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Political Party</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowPartyModal(false)}
            >
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            {POLITICAL_PARTIES.filter(party => 
              !politicalParties.some(p => p.party === party)
            ).map((party) => (
              <TouchableOpacity
                key={party}
                style={styles.modalItem}
                onPress={() => addPoliticalParty(party)}
              >
                <Text style={styles.modalItemText}>{party}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </View>
  );

  return (
    <View style={styles.questionContainer}>
      <Text style={styles.questionText}>
        {question.id}. {question.text}
      </Text>
      
      {question.type === 'yesno' && renderYesNoQuestion()}
      {question.type === 'number' && renderNumberQuestion()}
      {question.type === 'text' && renderTextQuestion()}
      {question.type === 'time' && renderTimeQuestion()}
      {question.type === 'politicalparty' && renderPoliticalPartyQuestion()}
    </View>
  );
};

const styles = StyleSheet.create({
  questionContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
    lineHeight: 24,
  },
  responseContainer: {
    gap: 12,
  },
  yesNoContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  yesNoButton: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedYesButton: {
    backgroundColor: '#DCFCE7',
    borderColor: '#16A34A',
  },
  selectedNoButton: {
    backgroundColor: '#FEE2E2',
    borderColor: '#DC2626',
  },
  yesNoText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },
  selectedYesText: {
    color: '#16A34A',
  },
  selectedNoText: {
    color: '#DC2626',
  },
  textInputContainer: {
    marginTop: 8,
  },
  textInputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
    textAlignVertical: 'top',
  },
  numberInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
  },
  politicalPartiesContainer: {
    marginTop: 12,
    gap: 12,
  },
  partiesLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  partyItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  partyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  partyName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
    flex: 1,
  },
  removePartyButton: {
    padding: 4,
  },
  partyCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  partyCountLabel: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
  partyCountInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    fontSize: 14,
    color: '#111827',
    width: 80,
    textAlign: 'center',
  },
  addPartyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EFF6FF',
    borderWidth: 1,
    borderColor: '#BFDBFE',
    borderRadius: 8,
    paddingVertical: 12,
    gap: 8,
  },
  addPartyText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#3B82F6',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  modalCloseButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  modalItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  modalItemText: {
    fontSize: 16,
    color: '#111827',
  },
});

export default QuestionComponent;
