import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Modal,
    SafeAreaView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// Comprehensive Malawi locations database organized by district
const MALAWI_LOCATIONS_DB = {
  'Balaka': [
    { id: 'bal_1', name: 'Balaka Boma', district: 'Balaka', lat: -14.9833, lng: 34.9500 },
    { id: 'bal_2', name: 'Balaka District Hospital', district: 'Balaka', lat: -14.9833, lng: 34.9500 },
    { id: 'bal_3', name: 'Balaka Market', district: 'Balaka', lat: -14.9840, lng: 34.9510 },
    { id: 'bal_4', name: 'Balaka Secondary School', district: 'Balaka', lat: -14.9820, lng: 34.9490 },
    { id: 'bal_5', name: 'Balaka Police Station', district: 'Balaka', lat: -14.9825, lng: 34.9505 },
    { id: 'bal_6', name: 'Utale Trading Centre', district: 'Balaka', lat: -14.9900, lng: 34.9600 },
    { id: 'bal_7', name: 'Kankao Trading Centre', district: 'Balaka', lat: -14.9700, lng: 34.9400 },
    { id: 'bal_8', name: 'Phimbi Trading Centre', district: 'Balaka', lat: -14.9950, lng: 34.9550 },
  ],
  'Blantyre': [
    { id: 'bla_1', name: 'Blantyre City Centre', district: 'Blantyre', lat: -15.7861, lng: 35.0058 },
    { id: 'bla_2', name: 'Queen Elizabeth Central Hospital', district: 'Blantyre', lat: -15.7861, lng: 35.0058 },
    { id: 'bla_3', name: 'Kamuzu Stadium', district: 'Blantyre', lat: -15.7900, lng: 35.0100 },
    { id: 'bla_4', name: 'Chichiri Shopping Mall', district: 'Blantyre', lat: -15.8000, lng: 35.0200 },
    { id: 'bla_5', name: 'Ndirande Market', district: 'Blantyre', lat: -15.8100, lng: 35.0300 },
    { id: 'bla_6', name: 'Limbe Market', district: 'Blantyre', lat: -15.8050, lng: 35.0150 },
    { id: 'bla_7', name: 'Blantyre Railway Station', district: 'Blantyre', lat: -15.7850, lng: 35.0050 },
    { id: 'bla_8', name: 'Mandala House', district: 'Blantyre', lat: -15.7870, lng: 35.0070 },
    { id: 'bla_9', name: 'Soche Hill', district: 'Blantyre', lat: -15.7950, lng: 35.0250 },
    { id: 'bla_10', name: 'Chilomoni Township', district: 'Blantyre', lat: -15.8200, lng: 35.0400 },
  ],
  'Lilongwe': [
    { id: 'lil_1', name: 'Lilongwe City Centre', district: 'Lilongwe', lat: -13.9626, lng: 33.7741 },
    { id: 'lil_2', name: 'Kamuzu Central Hospital', district: 'Lilongwe', lat: -13.9626, lng: 33.7741 },
    { id: 'lil_3', name: 'Kamuzu International Airport', district: 'Lilongwe', lat: -13.7894, lng: 33.7831 },
    { id: 'lil_4', name: 'Area 25 Market', district: 'Lilongwe', lat: -13.9700, lng: 33.7800 },
    { id: 'lil_5', name: 'Bingu National Stadium', district: 'Lilongwe', lat: -13.9500, lng: 33.7600 },
    { id: 'lil_6', name: 'Parliament Building', district: 'Lilongwe', lat: -13.9650, lng: 33.7750 },
    { id: 'lil_7', name: 'Crossroads Mall', district: 'Lilongwe', lat: -13.9800, lng: 33.7900 },
    { id: 'lil_8', name: 'Kawale Township', district: 'Lilongwe', lat: -13.9400, lng: 33.7500 },
    { id: 'lil_9', name: 'Area 18 Market', district: 'Lilongwe', lat: -13.9900, lng: 33.8000 },
    { id: 'lil_10', name: 'Mgona Township', district: 'Lilongwe', lat: -13.9300, lng: 33.7400 },
  ],
  'Mzuzu': [
    { id: 'mzu_1', name: 'Mzuzu City Centre', district: 'Mzuzu', lat: -11.4607, lng: 34.0164 },
    { id: 'mzu_2', name: 'Mzuzu Central Hospital', district: 'Mzuzu', lat: -11.4607, lng: 34.0164 },
    { id: 'mzu_3', name: 'Mzuzu University', district: 'Mzuzu', lat: -11.4500, lng: 34.0100 },
    { id: 'mzu_4', name: 'Katoto Market', district: 'Mzuzu', lat: -11.4650, lng: 34.0200 },
    { id: 'mzu_5', name: 'Chibanja Township', district: 'Mzuzu', lat: -11.4700, lng: 34.0250 },
    { id: 'mzu_6', name: 'Mzuzu Stadium', district: 'Mzuzu', lat: -11.4550, lng: 34.0150 },
  ],
  'Zomba': [
    { id: 'zom_1', name: 'Zomba City Centre', district: 'Zomba', lat: -15.3850, lng: 35.3188 },
    { id: 'zom_2', name: 'Zomba Central Hospital', district: 'Zomba', lat: -15.3850, lng: 35.3188 },
    { id: 'zom_3', name: 'University of Malawi', district: 'Zomba', lat: -15.3900, lng: 35.3200 },
    { id: 'zom_4', name: 'Zomba Plateau', district: 'Zomba', lat: -15.3500, lng: 35.3000 },
    { id: 'zom_5', name: 'Zomba Market', district: 'Zomba', lat: -15.3860, lng: 35.3190 },
    { id: 'zom_6', name: 'Matawale Township', district: 'Zomba', lat: -15.3950, lng: 35.3250 },
  ],
  'Kasungu': [
    { id: 'kas_1', name: 'Kasungu Boma', district: 'Kasungu', lat: -12.5833, lng: 33.4833 },
    { id: 'kas_2', name: 'Kasungu District Hospital', district: 'Kasungu', lat: -12.5833, lng: 33.4833 },
    { id: 'kas_3', name: 'Kasungu Market', district: 'Kasungu', lat: -12.5840, lng: 33.4840 },
    { id: 'kas_4', name: 'Kasungu National Park Gate', district: 'Kasungu', lat: -12.5500, lng: 33.4500 },
  ],
  'Mangochi': [
    { id: 'man_1', name: 'Mangochi Boma', district: 'Mangochi', lat: -14.4784, lng: 35.2644 },
    { id: 'man_2', name: 'Mangochi District Hospital', district: 'Mangochi', lat: -14.4784, lng: 35.2644 },
    { id: 'man_3', name: 'Mangochi Market', district: 'Mangochi', lat: -14.4790, lng: 35.2650 },
    { id: 'man_4', name: 'Lake Malawi Shore', district: 'Mangochi', lat: -14.4700, lng: 35.2700 },
    { id: 'man_5', name: 'Monkey Bay', district: 'Mangochi', lat: -14.0667, lng: 34.9167 },
  ]
};

// Flatten all locations for general search
const ALL_LOCATIONS = Object.values(MALAWI_LOCATIONS_DB).flat();

interface LocationData {
  name: string;
  latitude: number;
  longitude: number;
}

interface LocationPickerProps {
  visible: boolean;
  onClose: () => void;
  onLocationSelect: (location: LocationData) => void;
  currentLocation?: LocationData;
  selectedDistrict?: string; // Filter locations by district
}

export default function LocationPicker({ visible, onClose, onLocationSelect, currentLocation, selectedDistrict }: LocationPickerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredLocations, setFilteredLocations] = useState<any[]>([]);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [manualCoordinates, setManualCoordinates] = useState({
    latitude: currentLocation?.latitude?.toString() || '',
    longitude: currentLocation?.longitude?.toString() || '',
    name: currentLocation?.name || ''
  });

  // Get initial locations based on selected district
  const getInitialLocations = () => {
    if (selectedDistrict && MALAWI_LOCATIONS_DB[selectedDistrict]) {
      return MALAWI_LOCATIONS_DB[selectedDistrict];
    }
    return ALL_LOCATIONS.slice(0, 10); // Show first 10 locations if no district selected
  };

  // Real-time search with district filtering
  useEffect(() => {
    const searchLocations = async () => {
      if (searchQuery.trim().length < 2) {
        // Show initial locations when search is empty or too short
        setFilteredLocations(getInitialLocations());
        return;
      }

      setIsSearching(true);

      // Simulate API delay for real-time feel
      const searchTimeout = setTimeout(() => {
        let searchResults = [];

        // First, search in selected district if available
        if (selectedDistrict && MALAWI_LOCATIONS_DB[selectedDistrict]) {
          searchResults = MALAWI_LOCATIONS_DB[selectedDistrict].filter(location =>
            location.name.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }

        // If no results in selected district or no district selected, search all locations
        if (searchResults.length === 0) {
          searchResults = ALL_LOCATIONS.filter(location =>
            location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            location.district.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }

        // Limit results to 15 for performance
        setFilteredLocations(searchResults.slice(0, 15));
        setIsSearching(false);
      }, 300); // 300ms delay for real-time search feel

      return () => clearTimeout(searchTimeout);
    };

    searchLocations();
  }, [searchQuery, selectedDistrict]);

  // Initialize locations when modal opens
  useEffect(() => {
    if (visible) {
      setFilteredLocations(getInitialLocations());
      setSearchQuery('');
    }
  }, [visible, selectedDistrict]);

  const getCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Location permission is required to get your current location.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      // Reverse geocode to get address
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      let locationName = 'Current Location';
      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        locationName = [address.name, address.city, address.region]
          .filter(Boolean)
          .join(', ') || 'Current Location';
      }

      const locationData: LocationData = {
        name: locationName,
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      onLocationSelect(locationData);
      onClose();
    } catch (error) {
      Alert.alert(
        'Location Error',
        'Unable to get your current location. Please try again or select manually.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsGettingLocation(false);
    }
  };

  const handleLocationSelect = (location: any) => {
    const locationData: LocationData = {
      name: location.name,
      latitude: location.lat,
      longitude: location.lng,
    };
    onLocationSelect(locationData);
    onClose();
  };

  const handleManualLocationSubmit = () => {
    const lat = parseFloat(manualCoordinates.latitude);
    const lng = parseFloat(manualCoordinates.longitude);

    if (isNaN(lat) || isNaN(lng)) {
      Alert.alert('Invalid Coordinates', 'Please enter valid latitude and longitude values.');
      return;
    }

    if (lat < -90 || lat > 90) {
      Alert.alert('Invalid Latitude', 'Latitude must be between -90 and 90.');
      return;
    }

    if (lng < -180 || lng > 180) {
      Alert.alert('Invalid Longitude', 'Longitude must be between -180 and 180.');
      return;
    }

    if (!manualCoordinates.name.trim()) {
      Alert.alert('Location Name Required', 'Please enter a name for this location.');
      return;
    }

    const locationData: LocationData = {
      name: manualCoordinates.name.trim(),
      latitude: lat,
      longitude: lng,
    };

    onLocationSelect(locationData);
    onClose();
  };

  const renderLocationItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.locationItem}
      onPress={() => handleLocationSelect(item)}
    >
      <View style={styles.locationInfo}>
        <Text style={styles.locationName}>{item.name}</Text>
        <Text style={styles.locationDistrict}>{item.district} District</Text>
        <Text style={styles.locationCoordinates}>
          {item.lat.toFixed(4)}, {item.lng.toFixed(4)}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#6B7280" />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Select Location</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
        </View>

        {/* Current Location Button */}
        <View style={styles.currentLocationContainer}>
          <TouchableOpacity
            style={[styles.currentLocationButton, isGettingLocation && styles.disabledButton]}
            onPress={getCurrentLocation}
            disabled={isGettingLocation}
          >
            {isGettingLocation ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="location" size={20} color="#FFFFFF" />
            )}
            <Text style={styles.currentLocationText}>
              {isGettingLocation ? 'Getting Location...' : 'Use Current Location'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Manual Coordinates Section */}
        <View style={styles.manualSection}>
          <Text style={styles.sectionTitle}>Enter Coordinates Manually</Text>
          
          <View style={styles.coordinateRow}>
            <View style={styles.coordinateInput}>
              <Text style={styles.inputLabel}>Latitude</Text>
              <TextInput
                style={styles.textInput}
                value={manualCoordinates.latitude}
                onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, latitude: text }))}
                placeholder="-13.9626"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.coordinateInput}>
              <Text style={styles.inputLabel}>Longitude</Text>
              <TextInput
                style={styles.textInput}
                value={manualCoordinates.longitude}
                onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, longitude: text }))}
                placeholder="33.7741"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.nameInputContainer}>
            <Text style={styles.inputLabel}>Location Name</Text>
            <TextInput
              style={styles.textInput}
              value={manualCoordinates.name}
              onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, name: text }))}
              placeholder="Enter location name..."
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleManualLocationSubmit}
          >
            <Text style={styles.submitButtonText}>Use These Coordinates</Text>
          </TouchableOpacity>
        </View>

        {/* Search Section */}
        <View style={styles.searchSection}>
          <View style={styles.searchHeader}>
            <Text style={styles.sectionTitle}>
              {selectedDistrict ? `Search in ${selectedDistrict} District` : 'Search Malawi Locations'}
            </Text>
            {selectedDistrict && (
              <View style={styles.districtBadge}>
                <Text style={styles.districtBadgeText}>{selectedDistrict}</Text>
              </View>
            )}
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#9CA3AF" />
            <TextInput
              style={styles.searchInput}
              placeholder={selectedDistrict ? `Search in ${selectedDistrict}...` : "Search locations or districts..."}
              placeholderTextColor="#9CA3AF"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {isSearching && (
              <ActivityIndicator size="small" color="#3B82F6" />
            )}
            {searchQuery.length > 0 && !isSearching && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name="close-circle" size={20} color="#9CA3AF" />
              </TouchableOpacity>
            )}
          </View>

          <FlatList
            data={filteredLocations}
            renderItem={renderLocationItem}
            keyExtractor={(item) => item.id}
            style={styles.locationsList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="location-outline" size={48} color="#D1D5DB" />
                <Text style={styles.emptyText}>
                  {searchQuery.trim()
                    ? `No locations found for "${searchQuery}"`
                    : selectedDistrict
                      ? `No locations available in ${selectedDistrict}`
                      : 'No locations found'
                  }
                </Text>
                {searchQuery.trim() && (
                  <Text style={styles.emptySubtext}>
                    Try searching with different keywords
                  </Text>
                )}
              </View>
            }
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  currentLocationContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 14,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  currentLocationText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  coordinateRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  coordinateInput: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
  },
  nameInputContainer: {
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  searchSection: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  districtBadge: {
    backgroundColor: '#EFF6FF',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  districtBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#3B82F6',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    marginLeft: 12,
  },
  clearButton: {
    padding: 4,
  },
  locationsList: {
    flex: 1,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  locationDistrict: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  locationCoordinates: {
    fontSize: 12,
    color: '#9CA3AF',
    fontFamily: 'monospace',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 4,
  },
});
