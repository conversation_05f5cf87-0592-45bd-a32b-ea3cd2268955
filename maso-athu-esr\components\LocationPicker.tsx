import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Modal,
    SafeAreaView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// Sample Malawi locations data (in a real app, this would come from an API)
const MALAWI_LOCATIONS = [
  { id: '1', name: 'Lilongwe City Centre', district: 'Lilongwe', lat: -13.9626, lng: 33.7741 },
  { id: '2', name: 'Kamuzu Central Hospital', district: 'Lilongwe', lat: -13.9626, lng: 33.7741 },
  { id: '3', name: 'Blantyre City Centre', district: 'Blantyre', lat: -15.7861, lng: 35.0058 },
  { id: '4', name: 'Queen Elizabeth Central Hospital', district: 'Blantyre', lat: -15.7861, lng: 35.0058 },
  { id: '5', name: 'Mzuzu City Centre', district: 'Mzu<PERSON>', lat: -11.4607, lng: 34.0164 },
  { id: '6', name: 'Mzuzu Central Hospital', district: 'Mzuzu', lat: -11.4607, lng: 34.0164 },
  { id: '7', name: 'Zomba City Centre', district: 'Zomba', lat: -15.3850, lng: 35.3188 },
  { id: '8', name: 'Zomba Central Hospital', district: 'Zomba', lat: -15.3850, lng: 35.3188 },
  { id: '9', name: 'Kasungu Boma', district: 'Kasungu', lat: -12.5833, lng: 33.4833 },
  { id: '10', name: 'Kasungu District Hospital', district: 'Kasungu', lat: -12.5833, lng: 33.4833 },
  { id: '11', name: 'Mangochi Boma', district: 'Mangochi', lat: -14.4784, lng: 35.2644 },
  { id: '12', name: 'Mangochi District Hospital', district: 'Mangochi', lat: -14.4784, lng: 35.2644 },
  { id: '13', name: 'Mchinji Boma', district: 'Mchinji', lat: -13.7989, lng: 32.8997 },
  { id: '14', name: 'Dedza Boma', district: 'Dedza', lat: -14.3779, lng: 34.3332 },
  { id: '15', name: 'Ntcheu Boma', district: 'Ntcheu', lat: -14.7000, lng: 34.6167 },
  { id: '16', name: 'Balaka Boma', district: 'Balaka', lat: -14.9833, lng: 34.9500 },
  { id: '17', name: 'Machinga Boma', district: 'Machinga', lat: -14.9667, lng: 35.5167 },
  { id: '18', name: 'Nsanje Boma', district: 'Nsanje', lat: -16.9167, lng: 35.2667 },
  { id: '19', name: 'Chikwawa Boma', district: 'Chikwawa', lat: -16.0333, lng: 34.7833 },
  { id: '20', name: 'Thyolo Boma', district: 'Thyolo', lat: -16.0667, lng: 35.1333 }
];

interface LocationData {
  name: string;
  latitude: number;
  longitude: number;
}

interface LocationPickerProps {
  visible: boolean;
  onClose: () => void;
  onLocationSelect: (location: LocationData) => void;
  currentLocation?: LocationData;
}

export default function LocationPicker({ visible, onClose, onLocationSelect, currentLocation }: LocationPickerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredLocations, setFilteredLocations] = useState(MALAWI_LOCATIONS);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [manualCoordinates, setManualCoordinates] = useState({
    latitude: currentLocation?.latitude?.toString() || '',
    longitude: currentLocation?.longitude?.toString() || '',
    name: currentLocation?.name || ''
  });

  // Filter locations based on search query
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = MALAWI_LOCATIONS.filter(location =>
        location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.district.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredLocations(filtered);
    } else {
      setFilteredLocations(MALAWI_LOCATIONS);
    }
  }, [searchQuery]);

  const getCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Location permission is required to get your current location.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      // Reverse geocode to get address
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      let locationName = 'Current Location';
      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        locationName = [address.name, address.city, address.region]
          .filter(Boolean)
          .join(', ') || 'Current Location';
      }

      const locationData: LocationData = {
        name: locationName,
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      onLocationSelect(locationData);
      onClose();
    } catch (error) {
      Alert.alert(
        'Location Error',
        'Unable to get your current location. Please try again or select manually.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsGettingLocation(false);
    }
  };

  const handleLocationSelect = (location: any) => {
    const locationData: LocationData = {
      name: location.name,
      latitude: location.lat,
      longitude: location.lng,
    };
    onLocationSelect(locationData);
    onClose();
  };

  const handleManualLocationSubmit = () => {
    const lat = parseFloat(manualCoordinates.latitude);
    const lng = parseFloat(manualCoordinates.longitude);

    if (isNaN(lat) || isNaN(lng)) {
      Alert.alert('Invalid Coordinates', 'Please enter valid latitude and longitude values.');
      return;
    }

    if (lat < -90 || lat > 90) {
      Alert.alert('Invalid Latitude', 'Latitude must be between -90 and 90.');
      return;
    }

    if (lng < -180 || lng > 180) {
      Alert.alert('Invalid Longitude', 'Longitude must be between -180 and 180.');
      return;
    }

    if (!manualCoordinates.name.trim()) {
      Alert.alert('Location Name Required', 'Please enter a name for this location.');
      return;
    }

    const locationData: LocationData = {
      name: manualCoordinates.name.trim(),
      latitude: lat,
      longitude: lng,
    };

    onLocationSelect(locationData);
    onClose();
  };

  const renderLocationItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.locationItem}
      onPress={() => handleLocationSelect(item)}
    >
      <View style={styles.locationInfo}>
        <Text style={styles.locationName}>{item.name}</Text>
        <Text style={styles.locationDistrict}>{item.district} District</Text>
        <Text style={styles.locationCoordinates}>
          {item.lat.toFixed(4)}, {item.lng.toFixed(4)}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#6B7280" />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Select Location</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
        </View>

        {/* Current Location Button */}
        <View style={styles.currentLocationContainer}>
          <TouchableOpacity
            style={[styles.currentLocationButton, isGettingLocation && styles.disabledButton]}
            onPress={getCurrentLocation}
            disabled={isGettingLocation}
          >
            {isGettingLocation ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="location" size={20} color="#FFFFFF" />
            )}
            <Text style={styles.currentLocationText}>
              {isGettingLocation ? 'Getting Location...' : 'Use Current Location'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Manual Coordinates Section */}
        <View style={styles.manualSection}>
          <Text style={styles.sectionTitle}>Enter Coordinates Manually</Text>
          
          <View style={styles.coordinateRow}>
            <View style={styles.coordinateInput}>
              <Text style={styles.inputLabel}>Latitude</Text>
              <TextInput
                style={styles.textInput}
                value={manualCoordinates.latitude}
                onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, latitude: text }))}
                placeholder="-13.9626"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.coordinateInput}>
              <Text style={styles.inputLabel}>Longitude</Text>
              <TextInput
                style={styles.textInput}
                value={manualCoordinates.longitude}
                onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, longitude: text }))}
                placeholder="33.7741"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.nameInputContainer}>
            <Text style={styles.inputLabel}>Location Name</Text>
            <TextInput
              style={styles.textInput}
              value={manualCoordinates.name}
              onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, name: text }))}
              placeholder="Enter location name..."
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleManualLocationSubmit}
          >
            <Text style={styles.submitButtonText}>Use These Coordinates</Text>
          </TouchableOpacity>
        </View>

        {/* Search Section */}
        <View style={styles.searchSection}>
          <Text style={styles.sectionTitle}>Search Malawi Locations</Text>
          
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#9CA3AF" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search locations or districts..."
              placeholderTextColor="#9CA3AF"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name="close-circle" size={20} color="#9CA3AF" />
              </TouchableOpacity>
            )}
          </View>

          <FlatList
            data={filteredLocations}
            renderItem={renderLocationItem}
            keyExtractor={(item) => item.id}
            style={styles.locationsList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No locations found</Text>
              </View>
            }
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  currentLocationContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 14,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  currentLocationText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  coordinateRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  coordinateInput: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
  },
  nameInputContainer: {
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  searchSection: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    marginLeft: 12,
  },
  clearButton: {
    padding: 4,
  },
  locationsList: {
    flex: 1,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  locationDistrict: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  locationCoordinates: {
    fontSize: 12,
    color: '#9CA3AF',
    fontFamily: 'monospace',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
  },
});
