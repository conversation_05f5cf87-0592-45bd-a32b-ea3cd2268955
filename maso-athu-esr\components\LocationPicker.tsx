import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    <PERSON>ert,
    FlatList,
    Modal,
    SafeAreaView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// OpenStreetMap Nominatim API configuration
const NOMINATIM_BASE_URL = 'https://nominatim.openstreetmap.org/search';
const SEARCH_DELAY = 500; // Delay in ms to avoid overwhelming the API

// Cache for search results to improve performance
const searchCache = new Map();

// OpenStreetMap search function
const searchOpenStreetMap = async (query: string, district?: string): Promise<any[]> => {
  try {
    // Create cache key
    const cacheKey = `${query}-${district || 'all'}`;

    // Check cache first
    if (searchCache.has(cacheKey)) {
      return searchCache.get(cacheKey);
    }

    // Build search query
    let searchQuery = query;
    if (district) {
      searchQuery = `${query} ${district}`;
    }
    searchQuery += ' Malawi'; // Always include Malawi to limit results

    // Build URL with parameters
    const params = new URLSearchParams({
      format: 'json',
      q: searchQuery,
      countrycodes: 'MW', // Limit to Malawi
      limit: '15',
      addressdetails: '1',
      extratags: '1'
    });

    const url = `${NOMINATIM_BASE_URL}?${params.toString()}`;

    // Make API request
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'MasoAthu-ESR-App/1.0' // Required by Nominatim
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Transform results to our format
    const transformedResults = data.map((place: any, index: number) => {
      // Extract location name (first part of display_name)
      const nameParts = place.display_name.split(',');
      let locationName = nameParts[0].trim();

      // Try to get a better name from different fields
      if (place.name && place.name !== locationName) {
        locationName = place.name;
      }

      // Determine district from address components
      let detectedDistrict = district;
      if (!detectedDistrict && place.address) {
        // Try to extract district from address
        detectedDistrict = place.address.county ||
                          place.address.state_district ||
                          place.address.city ||
                          'Unknown';
      }

      return {
        id: place.place_id || `osm_${index}`,
        name: locationName,
        district: detectedDistrict || 'Malawi',
        lat: parseFloat(place.lat),
        lng: parseFloat(place.lon),
        address: place.display_name,
        type: place.type || 'location',
        importance: place.importance || 0
      };
    });

    // Sort by importance (higher is better)
    const sortedResults = transformedResults.sort((a, b) => b.importance - a.importance);

    // Cache results for 5 minutes
    searchCache.set(cacheKey, sortedResults);
    setTimeout(() => searchCache.delete(cacheKey), 5 * 60 * 1000);

    return sortedResults;
  } catch (error) {
    console.error('OpenStreetMap search error:', error);
    return [];
  }
};

interface LocationData {
  name: string;
  latitude: number;
  longitude: number;
}

interface LocationPickerProps {
  visible: boolean;
  onClose: () => void;
  onLocationSelect: (location: LocationData) => void;
  currentLocation?: LocationData;
  selectedDistrict?: string; // Filter locations by district
}

export default function LocationPicker({ visible, onClose, onLocationSelect, currentLocation, selectedDistrict }: LocationPickerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredLocations, setFilteredLocations] = useState<any[]>([]);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [manualCoordinates, setManualCoordinates] = useState({
    latitude: currentLocation?.latitude?.toString() || '',
    longitude: currentLocation?.longitude?.toString() || '',
    name: currentLocation?.name || ''
  });

  // Load initial popular locations for a district
  const loadInitialLocations = async (district?: string) => {
    setIsSearching(true);
    try {
      // Search for common places in the district
      const commonSearches = district
        ? [`${district} boma`, `${district} hospital`, `${district} market`]
        : ['Lilongwe', 'Blantyre', 'Mzuzu', 'Zomba'];

      const allResults = [];
      for (const search of commonSearches) {
        const results = await searchOpenStreetMap(search, district);
        allResults.push(...results.slice(0, 3)); // Take top 3 from each search
      }

      // Remove duplicates based on coordinates (within 100m)
      const uniqueResults = allResults.filter((location, index, arr) => {
        return !arr.slice(0, index).some(existing => {
          const distance = Math.sqrt(
            Math.pow(location.lat - existing.lat, 2) +
            Math.pow(location.lng - existing.lng, 2)
          );
          return distance < 0.001; // Roughly 100m
        });
      });

      setFilteredLocations(uniqueResults.slice(0, 10));
    } catch (error) {
      console.error('Error loading initial locations:', error);
      setFilteredLocations([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Real-time search with OpenStreetMap
  useEffect(() => {
    let searchTimeout: NodeJS.Timeout;

    const performSearch = async () => {
      if (searchQuery.trim().length < 2) {
        // Load initial locations when search is empty
        await loadInitialLocations(selectedDistrict);
        return;
      }

      setIsSearching(true);

      // Debounce search to avoid overwhelming the API
      searchTimeout = setTimeout(async () => {
        try {
          const results = await searchOpenStreetMap(searchQuery, selectedDistrict);
          setFilteredLocations(results);
        } catch (error) {
          console.error('Search error:', error);
          setFilteredLocations([]);
        } finally {
          setIsSearching(false);
        }
      }, SEARCH_DELAY);
    };

    performSearch();

    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchQuery, selectedDistrict]);

  // Initialize locations when modal opens
  useEffect(() => {
    if (visible) {
      setSearchQuery('');
      loadInitialLocations(selectedDistrict);
    }
  }, [visible, selectedDistrict]);

  const getCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Location permission is required to get your current location.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      // Reverse geocode to get address
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      let locationName = 'Current Location';
      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        locationName = [address.name, address.city, address.region]
          .filter(Boolean)
          .join(', ') || 'Current Location';
      }

      const locationData: LocationData = {
        name: locationName,
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      onLocationSelect(locationData);
      onClose();
    } catch (error) {
      Alert.alert(
        'Location Error',
        'Unable to get your current location. Please try again or select manually.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsGettingLocation(false);
    }
  };

  const handleLocationSelect = (location: any) => {
    const locationData: LocationData = {
      name: location.name,
      latitude: location.lat,
      longitude: location.lng,
    };
    onLocationSelect(locationData);
    onClose();
  };

  const handleManualLocationSubmit = () => {
    const lat = parseFloat(manualCoordinates.latitude);
    const lng = parseFloat(manualCoordinates.longitude);

    if (isNaN(lat) || isNaN(lng)) {
      Alert.alert('Invalid Coordinates', 'Please enter valid latitude and longitude values.');
      return;
    }

    if (lat < -90 || lat > 90) {
      Alert.alert('Invalid Latitude', 'Latitude must be between -90 and 90.');
      return;
    }

    if (lng < -180 || lng > 180) {
      Alert.alert('Invalid Longitude', 'Longitude must be between -180 and 180.');
      return;
    }

    if (!manualCoordinates.name.trim()) {
      Alert.alert('Location Name Required', 'Please enter a name for this location.');
      return;
    }

    const locationData: LocationData = {
      name: manualCoordinates.name.trim(),
      latitude: lat,
      longitude: lng,
    };

    onLocationSelect(locationData);
    onClose();
  };

  const renderLocationItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.locationItem}
      onPress={() => handleLocationSelect(item)}
    >
      <View style={styles.locationInfo}>
        <View style={styles.locationHeader}>
          <Text style={styles.locationName}>{item.name}</Text>
          {item.type && (
            <View style={styles.locationTypeBadge}>
              <Text style={styles.locationTypeText}>{item.type}</Text>
            </View>
          )}
        </View>
        <Text style={styles.locationDistrict}>{item.district}</Text>
        {item.address && (
          <Text style={styles.locationAddress} numberOfLines={1}>
            {item.address}
          </Text>
        )}
        <Text style={styles.locationCoordinates}>
          {item.lat.toFixed(4)}, {item.lng.toFixed(4)}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#6B7280" />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Select Location</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
        </View>

        {/* Current Location Button */}
        <View style={styles.currentLocationContainer}>
          <TouchableOpacity
            style={[styles.currentLocationButton, isGettingLocation && styles.disabledButton]}
            onPress={getCurrentLocation}
            disabled={isGettingLocation}
          >
            {isGettingLocation ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="location" size={20} color="#FFFFFF" />
            )}
            <Text style={styles.currentLocationText}>
              {isGettingLocation ? 'Getting Location...' : 'Use Current Location'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Manual Coordinates Section */}
        <View style={styles.manualSection}>
          <Text style={styles.sectionTitle}>Enter Coordinates Manually</Text>
          
          <View style={styles.coordinateRow}>
            <View style={styles.coordinateInput}>
              <Text style={styles.inputLabel}>Latitude</Text>
              <TextInput
                style={styles.textInput}
                value={manualCoordinates.latitude}
                onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, latitude: text }))}
                placeholder="-13.9626"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.coordinateInput}>
              <Text style={styles.inputLabel}>Longitude</Text>
              <TextInput
                style={styles.textInput}
                value={manualCoordinates.longitude}
                onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, longitude: text }))}
                placeholder="33.7741"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.nameInputContainer}>
            <Text style={styles.inputLabel}>Location Name</Text>
            <TextInput
              style={styles.textInput}
              value={manualCoordinates.name}
              onChangeText={(text) => setManualCoordinates(prev => ({ ...prev, name: text }))}
              placeholder="Enter location name..."
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleManualLocationSubmit}
          >
            <Text style={styles.submitButtonText}>Use These Coordinates</Text>
          </TouchableOpacity>
        </View>

        {/* Search Section */}
        <View style={styles.searchSection}>
          <View style={styles.searchHeader}>
            <Text style={styles.sectionTitle}>
              {selectedDistrict ? `Search in ${selectedDistrict} District` : 'Search Malawi Locations'}
            </Text>
            {selectedDistrict && (
              <View style={styles.districtBadge}>
                <Text style={styles.districtBadgeText}>{selectedDistrict}</Text>
              </View>
            )}
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#9CA3AF" />
            <TextInput
              style={styles.searchInput}
              placeholder={selectedDistrict ? `Search in ${selectedDistrict}...` : "Search locations or districts..."}
              placeholderTextColor="#9CA3AF"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {isSearching && (
              <ActivityIndicator size="small" color="#3B82F6" />
            )}
            {searchQuery.length > 0 && !isSearching && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name="close-circle" size={20} color="#9CA3AF" />
              </TouchableOpacity>
            )}
          </View>

          {isSearching && filteredLocations.length === 0 ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#3B82F6" />
              <Text style={styles.loadingText}>
                {searchQuery.trim() ? 'Searching locations...' : 'Loading locations...'}
              </Text>
            </View>
          ) : (
            <FlatList
              data={filteredLocations}
              renderItem={renderLocationItem}
              keyExtractor={(item) => item.id.toString()}
              style={styles.locationsList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                !isSearching ? (
                  <View style={styles.emptyContainer}>
                    <Ionicons name="location-outline" size={48} color="#D1D5DB" />
                    <Text style={styles.emptyText}>
                      {searchQuery.trim()
                        ? `No locations found for "${searchQuery}"`
                        : selectedDistrict
                          ? `No locations available in ${selectedDistrict}`
                          : 'No locations found'
                      }
                    </Text>
                    {searchQuery.trim() && (
                      <Text style={styles.emptySubtext}>
                        Try searching with different keywords or check your spelling
                      </Text>
                    )}
                    {!searchQuery.trim() && (
                      <Text style={styles.emptySubtext}>
                        Start typing to search for locations
                      </Text>
                    )}
                  </View>
                ) : null
              }
              ListHeaderComponent={
                filteredLocations.length > 0 && searchQuery.trim() ? (
                  <View style={styles.resultsHeader}>
                    <Text style={styles.resultsText}>
                      {filteredLocations.length} location{filteredLocations.length !== 1 ? 's' : ''} found
                    </Text>
                  </View>
                ) : null
              }
            />
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  currentLocationContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 14,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  currentLocationText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  manualSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  coordinateRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  coordinateInput: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
  },
  nameInputContainer: {
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  searchSection: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  districtBadge: {
    backgroundColor: '#EFF6FF',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  districtBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#3B82F6',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    marginLeft: 12,
  },
  clearButton: {
    padding: 4,
  },
  locationsList: {
    flex: 1,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  locationInfo: {
    flex: 1,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
    marginRight: 8,
  },
  locationTypeBadge: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  locationTypeText: {
    fontSize: 10,
    fontWeight: '500',
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  locationDistrict: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 12,
    color: '#9CA3AF',
    marginBottom: 4,
    lineHeight: 16,
  },
  locationCoordinates: {
    fontSize: 12,
    color: '#9CA3AF',
    fontFamily: 'monospace',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 16,
  },
  resultsHeader: {
    paddingVertical: 8,
    paddingHorizontal: 4,
    marginBottom: 8,
  },
  resultsText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
});
