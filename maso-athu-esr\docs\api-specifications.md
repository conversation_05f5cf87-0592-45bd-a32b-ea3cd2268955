# API Specifications

## Base URL
```
https://api.maso-athu.com/v1
```

## Authentication
All API requests require Bearer token authentication:
```
Authorization: Bearer <jwt_token>
```

## 1. INCIDENTS API

### POST /incidents - Create Incident
**Request Body:**
```json
{
  "title": "Polling station opened late",
  "title_chichewa": "Malo a vote anatseguka mochedwa",
  "description": "The polling station at Kamuzu Central Hospital opened 2 hours late due to missing ballot papers.",
  "description_chichewa": "Malo a vote a ku Kamuzu Central Hospital anatseguka maola awiri mochedwa chifukwa chosowa mapepala a vote.",
  "incident_type_id": 65,
  "incident_subtype_id": 651,
  "incident_date": "2024-12-15",
  "district": "Lilongwe",
  "location": "Kamuzu Central Hospital",
  "latitude": -13.9626,
  "longitude": 33.7741,
  "attachments": [
    {
      "file_name": "polling_station_photo.jpg",
      "file_data": "base64_encoded_string",
      "file_type": "image/jpeg"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "title": "Polling station opened late",
    "status": "submitted",
    "created_at": "2024-12-15T08:30:00Z"
  },
  "message": "Incident reported successfully"
}
```

### GET /incidents - Get User's Incidents
**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `status` (optional): Filter by status
- `district` (optional): Filter by district
- `search` (optional): Search in title/description

**Response:**
```json
{
  "success": true,
  "data": {
    "incidents": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "title": "Polling station opened late",
        "description": "The polling station at Kamuzu Central Hospital...",
        "incident_type": {
          "id": 65,
          "name": "Elections",
          "color": "#3B82F6"
        },
        "incident_subtype": {
          "id": 651,
          "name": "Delayed Opening",
          "code": "65.1"
        },
        "incident_date": "2024-12-15",
        "district": "Lilongwe",
        "location": "Kamuzu Central Hospital",
        "status": "submitted",
        "attachments_count": 2,
        "created_at": "2024-12-15T08:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 87,
      "items_per_page": 20
    }
  }
}
```

### GET /incidents/{id} - Get Incident Details
**Response:**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "title": "Polling station opened late",
    "title_chichewa": "Malo a vote anatseguka mochedwa",
    "description": "The polling station at Kamuzu Central Hospital...",
    "description_chichewa": "Malo a vote a ku Kamuzu Central Hospital...",
    "incident_type": {
      "id": 65,
      "name": "Elections",
      "name_chichewa": "Zisankho",
      "color": "#3B82F6"
    },
    "incident_subtype": {
      "id": 651,
      "name": "Delayed Opening",
      "code": "65.1"
    },
    "incident_date": "2024-12-15",
    "district": "Lilongwe",
    "location": "Kamuzu Central Hospital",
    "latitude": -13.9626,
    "longitude": 33.7741,
    "status": "submitted",
    "priority": "medium",
    "attachments": [
      {
        "id": "att-123",
        "file_name": "polling_station_photo.jpg",
        "file_url": "https://storage.maso-athu.com/attachments/att-123.jpg",
        "file_type": "image/jpeg",
        "file_size": 245760
      }
    ],
    "created_at": "2024-12-15T08:30:00Z",
    "updated_at": "2024-12-15T08:30:00Z"
  }
}
```

## 2. OBSERVER CHECKLISTS API

### POST /observer-checklists - Create/Update Observer Checklist
**Request Body:**
```json
{
  "district": "Lilongwe",
  "constituency": "Lilongwe City Centre",
  "ward": "Area 3",
  "polling_center": "Kamuzu Central Hospital",
  "timeline_period": "morning",
  "status": "completed",
  "responses": {
    "1a": {
      "answer": false,
      "textInput": "Station opened 2 hours late due to missing ballot papers"
    },
    "1b": {
      "answer": true
    },
    "2a": 5,
    "2b": 3,
    "2c": {
      "hasParties": true,
      "politicalParties": [
        {
          "party": "Malawi Congress Party (MCP)",
          "count": 2
        },
        {
          "party": "Democratic Progressive Party (DPP)",
          "count": 1
        }
      ]
    },
    "3a": {
      "answer": true
    }
  },
  "questions_answered": 43,
  "total_questions": 43
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "456e7890-e89b-12d3-a456-426614174001",
    "status": "completed",
    "submitted_at": "2024-12-15T10:30:00Z"
  },
  "message": "Observer checklist submitted successfully"
}
```

### GET /observer-checklists - Get User's Observer Checklists
**Query Parameters:**
- `page` (optional): Page number
- `limit` (optional): Items per page
- `status` (optional): Filter by status (draft, completed)
- `district` (optional): Filter by district
- `timeline_period` (optional): Filter by timeline
- `search` (optional): Search in location fields

**Response:**
```json
{
  "success": true,
  "data": {
    "checklists": [
      {
        "id": "456e7890-e89b-12d3-a456-426614174001",
        "district": "Lilongwe",
        "constituency": "Lilongwe City Centre",
        "ward": "Area 3",
        "polling_center": "Kamuzu Central Hospital",
        "timeline_period": "morning",
        "timeline_label": "Morning (6am - 10am)",
        "status": "completed",
        "questions_answered": 43,
        "total_questions": 43,
        "submitted_at": "2024-12-15T10:30:00Z",
        "created_at": "2024-12-15T08:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "total_items": 45,
      "items_per_page": 20
    }
  }
}
```

## 3. REFERENCE DATA APIs

### GET /incident-types - Get Incident Types
**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 61,
      "name": "Security",
      "name_chichewa": "Chitetezo",
      "color": "#EF4444",
      "subtypes": [
        {
          "id": 611,
          "name": "Violence",
          "code": "61.1"
        }
      ]
    }
  ]
}
```

### GET /polling-centers - Get Polling Centers
**Query Parameters:**
- `district` (optional): Filter by district
- `constituency` (optional): Filter by constituency
- `ward` (optional): Filter by ward

**Response:**
```json
{
  "success": true,
  "data": {
    "districts": [
      {
        "district_name": "Lilongwe",
        "constituencies": [
          {
            "constituency_name": "Lilongwe City Centre",
            "wards": [
              {
                "ward_name": "Area 3",
                "polling_centers": [
                  {
                    "center_name": "Kamuzu Central Hospital",
                    "center_code": "LLW-001",
                    "latitude": -13.9626,
                    "longitude": 33.7741
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "title": ["Title is required"],
      "incident_date": ["Date must be in YYYY-MM-DD format"]
    }
  }
}
```

## HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## 4. AUTHENTICATION APIs

### POST /auth/login - User Login
**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "username": "<EMAIL>",
      "full_name": "John Banda",
      "role": "observer"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-12-16T08:30:00Z"
  }
}
```

### POST /auth/register - User Registration
**Request Body:**
```json
{
  "username": "<EMAIL>",
  "email": "<EMAIL>",
  "password": "secure_password",
  "full_name": "Mary Phiri",
  "phone_number": "+265991234567"
}
```

### POST /auth/refresh - Refresh Token
**Request Body:**
```json
{
  "refresh_token": "refresh_token_here"
}
```

## 5. FILE UPLOAD API

### POST /upload - Upload Files
**Request:** Multipart form data
- `files[]`: File uploads
- `type`: "incident" or "profile"
- `description`: Optional file description

**Response:**
```json
{
  "success": true,
  "data": {
    "uploaded_files": [
      {
        "id": "file-123",
        "file_name": "incident_photo.jpg",
        "file_url": "https://storage.maso-athu.com/files/file-123.jpg",
        "file_type": "image/jpeg",
        "file_size": 245760
      }
    ]
  }
}
```
