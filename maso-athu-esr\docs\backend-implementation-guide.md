# Backend Implementation Guide

## Technology Stack Recommendations

### 1. Framework Options
- **Node.js + Express.js** (JavaScript/TypeScript)
- **Python + FastAPI** (Python)
- **PHP + Laravel** (PHP)
- **Java + Spring Boot** (Java)

### 2. Database
- **PostgreSQL** (Recommended for JSONB support and performance)
- **MySQL** (Alternative option)

### 3. File Storage
- **AWS S3** or **Google Cloud Storage** (Cloud storage)
- **Local storage** with proper backup strategy

### 4. Additional Services
- **Redis** (Caching and session management)
- **JWT** (Authentication tokens)
- **Cloudinary** (Image/video processing)

## Implementation Priorities

### Phase 1: Core APIs (Week 1-2)
1. **Authentication System**
   - User registration/login
   - JWT token management
   - Password hashing (bcrypt)

2. **Incident Management**
   - Create incident endpoint
   - Get incidents list with pagination
   - Get incident details
   - File upload handling

3. **Reference Data**
   - Incident types and subtypes
   - Polling centers data

### Phase 2: Observer Features (Week 3-4)
1. **Observer Checklists**
   - Create/update checklist endpoint
   - Get checklists list with filtering
   - JSONB response storage and querying

2. **Advanced Features**
   - Search functionality
   - Status management
   - Audit logging

### Phase 3: Admin & Analytics (Week 5-6)
1. **Admin Dashboard APIs**
   - Incident management
   - User management
   - Analytics endpoints

2. **Reporting**
   - Export functionality
   - Statistical reports

## Key Implementation Details

### 1. Database Considerations

#### JSONB for Observer Responses
```sql
-- Store responses as JSONB for flexibility
CREATE TABLE observer_checklists (
    -- ... other fields
    responses JSONB NOT NULL,
    -- Create GIN index for JSONB queries
    CONSTRAINT valid_responses CHECK (jsonb_typeof(responses) = 'object')
);

-- Create index for JSONB queries
CREATE INDEX idx_checklist_responses ON observer_checklists USING GIN (responses);

-- Example queries
-- Find checklists where question 1a was answered "false"
SELECT * FROM observer_checklists 
WHERE responses->'1a'->>'answer' = 'false';

-- Find checklists with political parties
SELECT * FROM observer_checklists 
WHERE responses->'2c'->>'hasParties' = 'true';
```

#### File Storage Strategy
```sql
-- Store file metadata in database
CREATE TABLE incident_attachments (
    id UUID PRIMARY KEY,
    incident_id UUID REFERENCES incidents(id),
    file_name VARCHAR(255),
    file_path VARCHAR(500), -- S3 key or local path
    file_url VARCHAR(500),  -- Public URL
    file_type VARCHAR(50),
    file_size INTEGER,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. API Security

#### Authentication Middleware
```javascript
// Example Express.js middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ 
      success: false, 
      error: { code: 'UNAUTHORIZED', message: 'Access token required' }
    });
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ 
        success: false, 
        error: { code: 'FORBIDDEN', message: 'Invalid token' }
      });
    }
    req.user = user;
    next();
  });
};
```

#### Input Validation
```javascript
// Example validation schema (using Joi)
const incidentSchema = Joi.object({
  title: Joi.string().min(5).max(200).required(),
  description: Joi.string().min(10).max(5000).required(),
  incident_type_id: Joi.number().integer().min(61).max(65).required(),
  incident_subtype_id: Joi.number().integer().required(),
  incident_date: Joi.date().iso().required(),
  district: Joi.string().min(2).max(100).required(),
  location: Joi.string().min(2).max(200).required(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional()
});
```

### 3. File Upload Handling

#### File Processing Pipeline
```javascript
// Example file upload handler
const uploadFiles = async (req, res) => {
  try {
    const files = req.files;
    const uploadedFiles = [];
    
    for (const file of files) {
      // Validate file type and size
      if (!isValidFileType(file.mimetype)) {
        throw new Error(`Invalid file type: ${file.mimetype}`);
      }
      
      if (file.size > getMaxFileSize(file.mimetype)) {
        throw new Error(`File too large: ${file.size} bytes`);
      }
      
      // Upload to S3 or local storage
      const fileUrl = await uploadToStorage(file);
      
      // Save metadata to database
      const fileRecord = await saveFileMetadata({
        file_name: file.originalname,
        file_path: fileUrl,
        file_type: file.mimetype,
        file_size: file.size
      });
      
      uploadedFiles.push(fileRecord);
    }
    
    res.json({ success: true, data: { uploaded_files: uploadedFiles } });
  } catch (error) {
    res.status(400).json({ 
      success: false, 
      error: { code: 'UPLOAD_ERROR', message: error.message }
    });
  }
};
```

### 4. Search Implementation

#### Full-Text Search
```sql
-- Add full-text search indexes
CREATE INDEX idx_incidents_search ON incidents 
USING GIN (to_tsvector('english', title || ' ' || description));

-- Search query example
SELECT * FROM incidents 
WHERE to_tsvector('english', title || ' ' || description) 
@@ plainto_tsquery('english', 'ballot box delay');
```

#### Observer Checklist Search
```sql
-- Search in JSONB responses
SELECT * FROM observer_checklists 
WHERE district ILIKE '%lilongwe%' 
   OR constituency ILIKE '%centre%'
   OR responses::text ILIKE '%delay%';
```

### 5. Performance Optimization

#### Database Indexes
```sql
-- Essential indexes for performance
CREATE INDEX idx_incidents_user_date ON incidents(user_id, incident_date DESC);
CREATE INDEX idx_incidents_district_type ON incidents(district, incident_type_id);
CREATE INDEX idx_checklists_location ON observer_checklists(district, constituency, ward);
CREATE INDEX idx_checklists_timeline_status ON observer_checklists(timeline_period, status);
```

#### Caching Strategy
```javascript
// Redis caching for reference data
const getIncidentTypes = async () => {
  const cacheKey = 'incident_types';
  let types = await redis.get(cacheKey);
  
  if (!types) {
    types = await database.query('SELECT * FROM incident_types ORDER BY id');
    await redis.setex(cacheKey, 3600, JSON.stringify(types)); // Cache for 1 hour
  } else {
    types = JSON.parse(types);
  }
  
  return types;
};
```

### 6. Error Handling

#### Standardized Error Response
```javascript
class APIError extends Error {
  constructor(code, message, statusCode = 400, details = null) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

// Error handling middleware
const errorHandler = (err, req, res, next) => {
  if (err instanceof APIError) {
    return res.status(err.statusCode).json({
      success: false,
      error: {
        code: err.code,
        message: err.message,
        details: err.details
      }
    });
  }
  
  // Log unexpected errors
  console.error('Unexpected error:', err);
  
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    }
  });
};
```

## Testing Strategy

### 1. Unit Tests
- Test individual functions and methods
- Mock external dependencies
- Validate business logic

### 2. Integration Tests
- Test API endpoints
- Validate database operations
- Test file upload functionality

### 3. Load Testing
- Test with concurrent users
- Validate performance under load
- Test file upload limits

## Deployment Considerations

### 1. Environment Variables
```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/maso_athu
DATABASE_POOL_SIZE=20

# JWT
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# File Storage
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_S3_BUCKET=maso-athu-files
AWS_REGION=us-east-1

# Redis
REDIS_URL=redis://localhost:6379

# App
PORT=3000
NODE_ENV=production
```

### 2. Docker Configuration
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

This comprehensive guide provides your backend developers with everything they need to implement the APIs that conform to your frontend requirements!
