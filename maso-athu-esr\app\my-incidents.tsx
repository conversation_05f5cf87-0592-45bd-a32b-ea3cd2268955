import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useMemo, useState } from 'react';
import {
    Alert,
    Platform,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

// Mock data for incidents
const MOCK_INCIDENTS = [
  {
    id: '1',
    title: 'Voter intimidation at polling station',
    description: 'Witnessed intimidation of voters by unknown individuals outside polling station 001',
    incidentType: 'Elections (65)',
    subCategory: 'Voter intimidation and coercion (3)',
    date: '15/12/2024',
    district: 'Lilongwe',
    location: 'Polling Station 001, Area 25',
    status: 'Submitted',
    priority: 'High',
    attachments: 2,
  },
  {
    id: '2',
    title: 'Corruption in local government',
    description: 'Evidence of bribery in tender allocation process at district council',
    incidentType: 'Governance and Human Rights Violations (64)',
    subCategory: 'Corruption and bribery (1)',
    date: '14/12/2024',
    district: 'Blantyre',
    location: 'District Council Offices',
    status: 'Under Review',
    priority: 'Medium',
    attachments: 1,
  },
  {
    id: '3',
    title: 'Gender-based violence incident',
    description: 'Reported case of domestic violence requiring immediate attention',
    incidentType: 'Gender (63)',
    subCategory: 'Physical violence (1)',
    date: '13/12/2024',
    district: 'Mzuzu',
    location: 'Mzimba Village',
    status: 'Resolved',
    priority: 'High',
    attachments: 3,
  },
  {
    id: '4',
    title: 'Environmental pollution',
    description: 'Illegal dumping of industrial waste near water source',
    incidentType: 'Health and Environment (62)',
    subCategory: 'Environmental pollution or illegal dumping (3)',
    date: '12/12/2024',
    district: 'Zomba',
    location: 'Zomba Industrial Area',
    status: 'Submitted',
    priority: 'Medium',
    attachments: 4,
  },
  {
    id: '5',
    title: 'Armed robbery incident',
    description: 'Armed robbery at local market affecting multiple vendors',
    incidentType: 'Security (61)',
    subCategory: 'Robbery (3)',
    date: '11/12/2024',
    district: 'Kasungu',
    location: 'Central Market',
    status: 'Under Review',
    priority: 'High',
    attachments: 1,
  },
];

export default function MyIncidentsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [showFilters, setShowFilters] = useState(false);

  const filterOptions = ['All', 'Submitted', 'Under Review', 'Resolved'];

  // Filter and search incidents
  const filteredIncidents = useMemo(() => {
    let filtered = MOCK_INCIDENTS;

    // Apply status filter
    if (selectedFilter !== 'All') {
      filtered = filtered.filter(incident => incident.status === selectedFilter);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(incident =>
        incident.title.toLowerCase().includes(query) ||
        incident.description.toLowerCase().includes(query) ||
        incident.incidentType.toLowerCase().includes(query) ||
        incident.subCategory.toLowerCase().includes(query) ||
        incident.district.toLowerCase().includes(query) ||
        incident.location.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [searchQuery, selectedFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Submitted':
        return '#3B82F6';
      case 'Under Review':
        return '#F59E0B';
      case 'Resolved':
        return '#10B981';
      default:
        return '#6B7280';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return '#EF4444';
      case 'Medium':
        return '#F59E0B';
      case 'Low':
        return '#10B981';
      default:
        return '#6B7280';
    }
  };

  const handleIncidentPress = (incident: any) => {
    Alert.alert(
      'Incident Details',
      `Title: ${incident.title}\n\nDescription: ${incident.description}\n\nStatus: ${incident.status}`,
      [
        { text: 'Edit', onPress: () => console.log('Edit incident') },
        { text: 'View Details', onPress: () => console.log('View details') },
        { text: 'Close', style: 'cancel' }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerIconContainer}>
            <Ionicons name="document-text" size={28} color="#3B82F6" />
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>My Incidents</Text>
            <Text style={styles.headerSubtitle}>
              {filteredIncidents.length} incident{filteredIncidents.length !== 1 ? 's' : ''} found
            </Text>
          </View>
        </View>
      </View>

      {/* Search and Filter Section */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search incidents..."
            placeholderTextColor="#9CA3AF"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#6B7280" />
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Ionicons name="filter" size={20} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      {/* Filter Options */}
      {showFilters && (
        <View style={styles.filterContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {filterOptions.map((filter) => (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.filterChip,
                  selectedFilter === filter && styles.filterChipActive
                ]}
                onPress={() => setSelectedFilter(filter)}
              >
                <Text style={[
                  styles.filterChipText,
                  selectedFilter === filter && styles.filterChipTextActive
                ]}>
                  {filter}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Incidents List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredIncidents.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color="#9CA3AF" />
            <Text style={styles.emptyStateTitle}>No incidents found</Text>
            <Text style={styles.emptyStateText}>
              {searchQuery ? 'Try adjusting your search terms' : 'You haven\'t reported any incidents yet'}
            </Text>
            {!searchQuery && (
              <TouchableOpacity 
                style={styles.addIncidentButton}
                onPress={() => router.push('/report-incident')}
              >
                <Text style={styles.addIncidentButtonText}>Report New Incident</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.incidentsList}>
            {filteredIncidents.map((incident) => (
              <TouchableOpacity
                key={incident.id}
                style={styles.incidentCard}
                onPress={() => handleIncidentPress(incident)}
              >
                <View style={styles.incidentHeader}>
                  <View style={styles.incidentTitleContainer}>
                    <Text style={styles.incidentTitle} numberOfLines={2}>
                      {incident.title}
                    </Text>
                    <View style={styles.incidentMeta}>
                      <Text style={styles.incidentDate}>{incident.date}</Text>
                      <Text style={styles.incidentLocation}>{incident.district}</Text>
                    </View>
                  </View>
                  <View style={styles.incidentBadges}>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(incident.status) + '20' }]}>
                      <Text style={[styles.statusText, { color: getStatusColor(incident.status) }]}>
                        {incident.status}
                      </Text>
                    </View>
                    <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(incident.priority) + '20' }]}>
                      <Text style={[styles.priorityText, { color: getPriorityColor(incident.priority) }]}>
                        {incident.priority}
                      </Text>
                    </View>
                  </View>
                </View>

                <Text style={styles.incidentDescription} numberOfLines={2}>
                  {incident.description}
                </Text>

                <View style={styles.incidentFooter}>
                  <View style={styles.incidentType}>
                    <Text style={styles.incidentTypeText} numberOfLines={1}>
                      {incident.incidentType}
                    </Text>
                    <Text style={styles.incidentSubCategory} numberOfLines={1}>
                      {incident.subCategory}
                    </Text>
                  </View>
                  
                  {incident.attachments > 0 && (
                    <View style={styles.attachmentInfo}>
                      <Ionicons name="attach" size={16} color="#6B7280" />
                      <Text style={styles.attachmentCount}>{incident.attachments}</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity 
        style={styles.fab}
        onPress={() => router.push('/report-incident')}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#EBF4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  searchSection: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    paddingVertical: Platform.OS === 'ios' ? 12 : 10,
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: '#EBF4FF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  filterContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterChipActive: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  filterChipTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  addIncidentButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  addIncidentButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  incidentsList: {
    padding: 20,
    gap: 16,
  },
  incidentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  incidentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  incidentTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  incidentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 6,
    lineHeight: 24,
  },
  incidentMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  incidentDate: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  incidentLocation: {
    fontSize: 14,
    color: '#6B7280',
  },
  incidentBadges: {
    gap: 6,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  incidentDescription: {
    fontSize: 15,
    color: '#4B5563',
    lineHeight: 22,
    marginBottom: 16,
  },
  incidentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  incidentType: {
    flex: 1,
    marginRight: 12,
  },
  incidentTypeText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#3B82F6',
    marginBottom: 2,
  },
  incidentSubCategory: {
    fontSize: 12,
    color: '#6B7280',
  },
  attachmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  attachmentCount: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
});
