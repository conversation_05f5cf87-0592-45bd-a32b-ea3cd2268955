# JSON Format Examples

## 1. INCIDENT SUBMISSION

### Complete Incident Submission Example
```json
{
  "title": "Ballot boxes arrived late at polling station",
  "title_chichewa": "Mabo<PERSON>i a mavoti anafika mochedwa ku malo a vote",
  "description": "The ballot boxes for the morning session arrived 3 hours late at Kamuzu Central Hospital polling station. This caused significant delays in the voting process and long queues of frustrated voters. The delay was attributed to transportation issues from the district office.",
  "description_chichewa": "Mabokosi a mavoti a m'mawa anafika maola atatu mochedwa ku malo a vote a ku Kamuzu Central Hospital. Izi zinayambitsa kuchedwa kwakukulu pa ntchito ya vote ndi mizere yayitali ya ovota okwiya. Kuchedwaku kunachitika chifukwa cha mavuto a mayendedwe kuchokera ku ofesi ya boma.",
  "incident_type_id": 65,
  "incident_subtype_id": 652,
  "incident_date": "2024-12-15",
  "district": "Lilongwe",
  "location": "Kamuzu Central Hospital - Main Entrance",
  "latitude": -13.9626,
  "longitude": 33.7741,
  "attachments": [
    {
      "file_name": "late_ballot_boxes.jpg",
      "file_data": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
      "file_type": "image/jpeg"
    },
    {
      "file_name": "voter_queue_video.mp4",
      "file_data": "base64_encoded_video_data_here",
      "file_type": "video/mp4"
    }
  ]
}
```

### Incident Types Reference
```json
{
  "incident_types": {
    "61": {
      "name": "Security",
      "name_chichewa": "Chitetezo",
      "subtypes": {
        "611": "Violence/Intimidation",
        "612": "Theft/Vandalism",
        "613": "Unauthorized Access"
      }
    },
    "62": {
      "name": "Health and Environment",
      "name_chichewa": "Thanzi ndi Chilengedwe",
      "subtypes": {
        "621": "Health Emergency",
        "622": "Environmental Hazard",
        "623": "Sanitation Issues"
      }
    },
    "63": {
      "name": "Gender",
      "name_chichewa": "Kusiyana kwa Amuna ndi Akazi",
      "subtypes": {
        "631": "Gender-based Violence",
        "632": "Discrimination",
        "633": "Access Barriers"
      }
    },
    "64": {
      "name": "Governance and Human Rights Violations",
      "name_chichewa": "Ulamuliro ndi Kuphwanya Ufulu wa Anthu",
      "subtypes": {
        "641": "Corruption",
        "642": "Rights Violation",
        "643": "Abuse of Power"
      }
    },
    "65": {
      "name": "Elections",
      "name_chichewa": "Zisankho",
      "subtypes": {
        "651": "Delayed Opening",
        "652": "Missing Materials",
        "653": "Procedural Violations",
        "654": "Vote Buying",
        "655": "Ballot Issues"
      }
    }
  }
}
```

## 2. OBSERVER CHECKLIST SUBMISSION

### Morning Timeline Complete Example
```json
{
  "district": "Lilongwe",
  "constituency": "Lilongwe City Centre",
  "ward": "Area 3",
  "polling_center": "Kamuzu Central Hospital",
  "timeline_period": "morning",
  "status": "completed",
  "responses": {
    "1a": {
      "answer": false,
      "textInput": "Station opened 2 hours late due to missing ballot papers and late arrival of election officials"
    },
    "1b": {
      "answer": true
    },
    "2a": 5,
    "2b": 3,
    "2c": {
      "hasParties": true,
      "politicalParties": [
        {
          "party": "Malawi Congress Party (MCP)",
          "count": 2
        },
        {
          "party": "Democratic Progressive Party (DPP)",
          "count": 1
        },
        {
          "party": "United Transformation Movement (UTM)",
          "count": 1
        }
      ]
    },
    "3a": {
      "answer": true
    },
    "3b": {
      "answer": true
    },
    "4a": {
      "answer": true
    },
    "4b": 500,
    "4c": 3,
    "4d": 2,
    "4e": 4,
    "4f": 10,
    "4g": 2,
    "4h": {
      "answer": true,
      "textInput": "Embossing device malfunctioned for 30 minutes, resolved by technician"
    },
    "5a": {
      "answer": true
    },
    "5b": {
      "answer": true
    },
    "5c": {
      "answer": false
    },
    "5d": {
      "answer": true
    },
    "5e": {
      "answer": true
    },
    "5f": 12,
    "5g": 8,
    "6a": {
      "answer": true
    },
    "6b": {
      "answer": true
    },
    "6c": {
      "answer": false
    },
    "6d": {
      "answer": false
    },
    "6e": {
      "answer": true
    },
    "6f": 0,
    "6g": {
      "answer": true
    },
    "6h": {
      "answer": true
    },
    "6i": {
      "answer": true
    },
    "6j": {
      "answer": true
    },
    "7a": {
      "answer": false,
      "textInput": "Queue moved slowly due to technical issues with voter verification"
    },
    "7b": {
      "answer": true
    },
    "7c": {
      "answer": true
    },
    "7d": {
      "answer": true
    },
    "8a": {
      "answer": true
    },
    "8b": {
      "answer": true
    },
    "8c": {
      "answer": true
    },
    "8d": {
      "answer": true
    },
    "9a": {
      "answer": true
    },
    "9b": {
      "answer": true
    },
    "9c": {
      "answer": true,
      "textInput": "One complaint about slow queue movement, resolved by station manager"
    },
    "9d": 1
  },
  "questions_answered": 43,
  "total_questions": 43
}
```

### Vote Count Timeline Example
```json
{
  "district": "Blantyre",
  "constituency": "Blantyre City South",
  "ward": "Ndirande",
  "polling_center": "Ndirande Community Centre",
  "timeline_period": "votecount",
  "status": "completed",
  "responses": {
    "30a": "16:30",
    "31a": {
      "answer": true
    },
    "31b": {
      "answer": true
    },
    "32a": {
      "answer": false
    },
    "32b": 45,
    "32c": 12,
    "32d": 8,
    "32e": {
      "answer": true
    },
    "32f": {
      "answer": true
    },
    "32g": {
      "answer": true
    }
  },
  "questions_answered": 10,
  "total_questions": 10
}
```

## 3. RESPONSE DATA FORMATS

### Expected Response Formats by Question Type

#### Yes/No Questions
```json
{
  "question_id": {
    "answer": true  // or false
  }
}
```

#### Yes/No with Text Input (when No is selected)
```json
{
  "question_id": {
    "answer": false,
    "textInput": "Explanation text here"
  }
}
```

#### Number Questions
```json
{
  "question_id": 25  // integer value
}
```

#### Text Questions
```json
{
  "question_id": "Free text response here"
}
```

#### Time Questions
```json
{
  "question_id": "16:30"  // HH:MM format
}
```

#### Political Party Questions
```json
{
  "question_id": {
    "hasParties": true,
    "politicalParties": [
      {
        "party": "Malawi Congress Party (MCP)",
        "count": 2
      },
      {
        "party": "Democratic Progressive Party (DPP)",
        "count": 1
      }
    ]
  }
}
```

#### Political Party Questions (No Parties)
```json
{
  "question_id": {
    "hasParties": false,
    "politicalParties": []
  }
}
```

## 4. FILE UPLOAD FORMAT

### Attachment Structure
```json
{
  "file_name": "incident_photo_001.jpg",
  "file_data": "base64_encoded_string_here",
  "file_type": "image/jpeg",
  "file_size": 245760,
  "description": "Photo showing the incident location"
}
```

### Supported File Types
- **Images**: `image/jpeg`, `image/png`, `image/gif`
- **Videos**: `video/mp4`, `video/avi`, `video/mov`
- **Documents**: `application/pdf`, `text/plain`

### File Size Limits
- **Images**: Max 5MB per file
- **Videos**: Max 50MB per file
- **Documents**: Max 10MB per file
